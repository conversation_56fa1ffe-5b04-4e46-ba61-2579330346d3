{"name": "hanuman-unified", "version": "1.0.0", "description": "🕉️ Hanuman - Organisme IA Vivant Biomimétique pour la protection de Retreat And Be", "main": "brain/cortex-central/HanumanCore.js", "scripts": {"start": "node brain/cortex-central/dist/HanumanCore.js", "dev": "ts-node brain/cortex-central/HanumanCore.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write .", "check-integrity": "./scripts/check-hanuman-integrity.sh", "start-hanuman": "./scripts/start-hanuman.sh", "stop-hanuman": "./scripts/stop-hanuman.sh", "health-check": "curl -s http://localhost:8080/hanuman/health", "logs": "tail -f logs/hanuman.log", "clean": "rm -rf dist/ logs/ pids/ node_modules/.cache", "install-all": "npm install && npm run install-agents", "install-agents": "cd specialized-agents && for dir in */; do (cd \"$dir\" && npm install); done", "build-all": "npm run build && npm run build-agents", "build-agents": "cd specialized-agents && for dir in */; do (cd \"$dir\" && npm run build); done"}, "keywords": ["ai", "biomimetic", "organism", "hanuman", "retreat-and-be", "protection", "monitoring", "automation", "intelligent-system"], "author": "Retreat And Be Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/retreat-and-be/hanuman-unified.git"}, "bugs": {"url": "https://github.com/retreat-and-be/hanuman-unified/issues"}, "homepage": "https://github.com/retreat-and-be/hanuman-unified#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@types/node": "^20.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.10.0", "dotenv": "^16.3.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "redis": "^4.6.7", "kafkajs": "^2.2.4", "ws": "^8.13.0", "axios": "^1.4.0", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0", "chalk": "^4.1.2", "inquirer": "^8.2.5", "commander": "^11.0.0", "chokidar": "^3.5.3", "node-cron": "^3.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "puppeteer": "^20.7.2", "cheerio": "^1.0.0-rc.12", "nodemailer": "^6.9.3", "socket.io": "^4.7.2", "prometheus-client": "^1.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/ws": "^8.5.5", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@types/inquirer": "^8.2.6", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.6.1", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.44.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "nodemon": "^3.0.1", "concurrently": "^8.2.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["**/*.ts", "!**/*.d.ts", "!**/node_modules/**", "!**/dist/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "config": {"hanuman": {"configFile": "./config/hanuman.config.json", "logLevel": "info", "port": 8080, "environment": "development"}}}