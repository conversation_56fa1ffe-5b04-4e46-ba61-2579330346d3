import React, { useState, useEffect, useRef } from 'react';
import { Shield, AlertTriangle, Zap, Activity, Eye, Lock, Wifi, Server, AlertCircle, CheckCircle } from 'lucide-react';

// Types pour l'interface du système immunitaire
interface ThreatDetection {
  id: string;
  type: 'malware' | 'intrusion' | 'anomaly' | 'vulnerability' | 'ddos' | 'injection';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  target: string;
  timestamp: Date;
  status: 'detected' | 'analyzing' | 'contained' | 'neutralized' | 'escalated';
  description: string;
  confidence: number;
}

interface ImmuneResponse {
  id: string;
  threatId: string;
  action: 'quarantine' | 'block' | 'monitor' | 'patch' | 'isolate' | 'alert';
  timestamp: Date;
  success: boolean;
  duration: number;
  details: string;
}

interface SecurityMetrics {
  threatsDetected: number;
  threatsNeutralized: number;
  activeThreats: number;
  systemHealth: number;
  responseTime: number;
  falsePositives: number;
}

interface SecurityAgent {
  id: string;
  name: string;
  type: 'scanner' | 'monitor' | 'analyzer' | 'responder';
  status: 'active' | 'idle' | 'busy' | 'offline';
  lastActivity: Date;
  threatsHandled: number;
  efficiency: number;
}

interface QuarantineItem {
  id: string;
  type: 'file' | 'process' | 'connection' | 'user';
  name: string;
  reason: string;
  timestamp: Date;
  risk: number;
  isolated: boolean;
}

const HanumanImmuneInterface = ({ darkMode = true }) => {
  const [threats, setThreats] = useState<ThreatDetection[]>([]);
  const [responses, setResponses] = useState<ImmuneResponse[]>([]);
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    threatsDetected: 127,
    threatsNeutralized: 119,
    activeThreats: 3,
    systemHealth: 0.94,
    responseTime: 1.2,
    falsePositives: 5
  });
  const [securityAgents, setSecurityAgents] = useState<SecurityAgent[]>([]);
  const [quarantine, setQuarantine] = useState<QuarantineItem[]>([]);
  const [selectedThreat, setSelectedThreat] = useState<string | null>(null);
  const [autoResponse, setAutoResponse] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);

  // Simulation des données de sécurité
  useEffect(() => {
    const mockThreats: ThreatDetection[] = [
      {
        id: 'threat-001',
        type: 'intrusion',
        severity: 'high',
        source: '*************',
        target: 'agent-backend',
        timestamp: new Date(),
        status: 'analyzing',
        description: 'Tentative d\'accès non autorisé détectée sur l\'API backend',
        confidence: 0.89
      },
      {
        id: 'threat-002',
        type: 'anomaly',
        severity: 'medium',
        source: 'agent-frontend',
        target: 'cortex-central',
        timestamp: new Date(Date.now() - 30000),
        status: 'contained',
        description: 'Comportement anormal détecté dans les patterns de communication',
        confidence: 0.76
      },
      {
        id: 'threat-003',
        type: 'vulnerability',
        severity: 'critical',
        source: 'external-scan',
        target: 'agent-security',
        timestamp: new Date(Date.now() - 60000),
        status: 'neutralized',
        description: 'Vulnérabilité critique détectée dans les dépendances',
        confidence: 0.95
      }
    ];
    setThreats(mockThreats);

    const mockResponses: ImmuneResponse[] = [
      {
        id: 'response-001',
        threatId: 'threat-003',
        action: 'patch',
        timestamp: new Date(Date.now() - 45000),
        success: true,
        duration: 15000,
        details: 'Patch de sécurité appliqué automatiquement'
      },
      {
        id: 'response-002',
        threatId: 'threat-002',
        action: 'quarantine',
        timestamp: new Date(Date.now() - 25000),
        success: true,
        duration: 5000,
        details: 'Processus suspect mis en quarantaine'
      }
    ];
    setResponses(mockResponses);

    const mockAgents: SecurityAgent[] = [
      {
        id: 'security-scanner',
        name: 'Scanner de Vulnérabilités',
        type: 'scanner',
        status: 'active',
        lastActivity: new Date(),
        threatsHandled: 45,
        efficiency: 0.92
      },
      {
        id: 'intrusion-monitor',
        name: 'Moniteur d\'Intrusion',
        type: 'monitor',
        status: 'busy',
        lastActivity: new Date(Date.now() - 15000),
        threatsHandled: 23,
        efficiency: 0.87
      },
      {
        id: 'threat-analyzer',
        name: 'Analyseur de Menaces',
        type: 'analyzer',
        status: 'active',
        lastActivity: new Date(Date.now() - 30000),
        threatsHandled: 67,
        efficiency: 0.94
      },
      {
        id: 'auto-responder',
        name: 'Répondeur Automatique',
        type: 'responder',
        status: 'idle',
        lastActivity: new Date(Date.now() - 60000),
        threatsHandled: 89,
        efficiency: 0.89
      }
    ];
    setSecurityAgents(mockAgents);

    const mockQuarantine: QuarantineItem[] = [
      {
        id: 'quar-001',
        type: 'process',
        name: 'suspicious_script.js',
        reason: 'Comportement malveillant détecté',
        timestamp: new Date(Date.now() - 120000),
        risk: 0.85,
        isolated: true
      },
      {
        id: 'quar-002',
        type: 'connection',
        name: '*************:8080',
        reason: 'Tentative d\'intrusion',
        timestamp: new Date(Date.now() - 180000),
        risk: 0.92,
        isolated: true
      }
    ];
    setQuarantine(mockQuarantine);
  }, []);

  // Connexion WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    // Simulation de connexion WebSocket avec le système immunitaire
    wsRef.current = new WebSocket('ws://localhost:8080/immune-system');
    
    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'threat_detected') {
        setThreats(prev => [data.threat, ...prev.slice(0, 9)]);
      } else if (data.type === 'response_executed') {
        setResponses(prev => [data.response, ...prev.slice(0, 9)]);
      } else if (data.type === 'metrics_update') {
        setMetrics(data.metrics);
      }
    };

    return () => {
      wsRef.current?.close();
    };
  }, []);

  const handleManualResponse = (threatId: string, action: string) => {
    // Déclencher une réponse manuelle
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'manual_response',
        threatId,
        action
      }));
    }
  };

  const releaseFromQuarantine = (itemId: string) => {
    // Libérer un élément de la quarantaine
    setQuarantine(prev => prev.filter(item => item.id !== itemId));
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'release_quarantine',
        itemId
      }));
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/20';
      case 'high': return 'text-orange-400 bg-orange-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'detected': return 'text-red-400';
      case 'analyzing': return 'text-yellow-400';
      case 'contained': return 'text-blue-400';
      case 'neutralized': return 'text-green-400';
      case 'escalated': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getThreatIcon = (type: string) => {
    switch (type) {
      case 'malware': return <AlertTriangle className="w-4 h-4" />;
      case 'intrusion': return <Eye className="w-4 h-4" />;
      case 'anomaly': return <Activity className="w-4 h-4" />;
      case 'vulnerability': return <AlertCircle className="w-4 h-4" />;
      case 'ddos': return <Wifi className="w-4 h-4" />;
      case 'injection': return <Server className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'busy': return 'text-yellow-400';
      case 'idle': return 'text-blue-400';
      case 'offline': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
                Système Immunitaire Hanuman
              </h1>
              <p className="text-gray-400 mt-1">
                Détection et neutralisation automatique des menaces
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">Réponse Auto</span>
              <button
                onClick={() => setAutoResponse(!autoResponse)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoResponse ? 'bg-green-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoResponse ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Menaces Détectées</p>
              <p className="text-2xl font-bold text-red-400">{metrics.threatsDetected}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-400" />
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Neutralisées</p>
              <p className="text-2xl font-bold text-green-400">{metrics.threatsNeutralized}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Menaces Actives</p>
              <p className="text-2xl font-bold text-orange-400">{metrics.activeThreats}</p>
            </div>
            <Activity className="w-8 h-8 text-orange-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Santé Système</p>
              <p className="text-2xl font-bold text-blue-400">{(metrics.systemHealth * 100).toFixed(0)}%</p>
            </div>
            <Shield className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Temps Réponse</p>
              <p className="text-2xl font-bold text-purple-400">{metrics.responseTime}s</p>
            </div>
            <Zap className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Faux Positifs</p>
              <p className="text-2xl font-bold text-yellow-400">{metrics.falsePositives}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-yellow-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Menaces détectées */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-400" />
            Menaces Détectées
          </h3>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {threats.map((threat) => (
              <div
                key={threat.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedThreat === threat.id
                    ? 'border-red-500 bg-red-500/10'
                    : darkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-100 hover:bg-gray-200'
                }`}
                onClick={() => setSelectedThreat(selectedThreat === threat.id ? null : threat.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <div className="text-red-400">
                      {getThreatIcon(threat.type)}
                    </div>
                    <div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(threat.severity)}`}>
                        {threat.severity.toUpperCase()}
                      </span>
                      <span className="ml-2 text-sm font-medium">{threat.type}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm ${getStatusColor(threat.status)}`}>
                      {threat.status}
                    </span>
                    <span className="text-xs text-gray-400">
                      {threat.confidence.toFixed(0)}%
                    </span>
                  </div>
                </div>
                
                <p className="text-sm text-gray-400 mb-2">{threat.description}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>Source: {threat.source}</span>
                  <span>Cible: {threat.target}</span>
                  <span>{threat.timestamp.toLocaleTimeString()}</span>
                </div>
                
                {selectedThreat === threat.id && (
                  <div className="mt-3 pt-3 border-t border-gray-600">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleManualResponse(threat.id, 'quarantine')}
                        className="px-3 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs rounded transition-colors"
                      >
                        Quarantaine
                      </button>
                      <button
                        onClick={() => handleManualResponse(threat.id, 'block')}
                        className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded transition-colors"
                      >
                        Bloquer
                      </button>
                      <button
                        onClick={() => handleManualResponse(threat.id, 'monitor')}
                        className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                      >
                        Surveiller
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Agents de sécurité */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Shield className="w-5 h-5 mr-2 text-blue-400" />
            Agents de Sécurité
          </h3>
          
          <div className="space-y-3">
            {securityAgents.map((agent) => (
              <div
                key={agent.id}
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <div className={getAgentStatusColor(agent.status)}>
                      <Activity className="w-4 h-4" />
                    </div>
                    <div>
                      <h4 className="font-medium">{agent.name}</h4>
                      <p className="text-sm text-gray-400">{agent.type}</p>
                    </div>
                  </div>
                  <span className={`text-sm ${getAgentStatusColor(agent.status)}`}>
                    {agent.status}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span>Menaces traitées: {agent.threatsHandled}</span>
                  <span>Efficacité: {(agent.efficiency * 100).toFixed(0)}%</span>
                </div>
                
                <div className="mt-2">
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${agent.efficiency * 100}%` }}
                    />
                  </div>
                </div>
                
                <p className="text-xs text-gray-400 mt-2">
                  Dernière activité: {agent.lastActivity.toLocaleTimeString()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quarantaine */}
      <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
        <h3 className="text-xl font-semibold mb-4 flex items-center">
          <Lock className="w-5 h-5 mr-2 text-yellow-400" />
          Zone de Quarantaine
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quarantine.map((item) => (
            <div
              key={item.id}
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Lock className="w-4 h-4 text-yellow-400" />
                  <span className="font-medium">{item.type}</span>
                </div>
                <span className="text-xs text-gray-400">
                  Risque: {(item.risk * 100).toFixed(0)}%
                </span>
              </div>
              
              <h4 className="font-medium mb-1">{item.name}</h4>
              <p className="text-sm text-gray-400 mb-3">{item.reason}</p>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">
                  {item.timestamp.toLocaleString()}
                </span>
                <button
                  onClick={() => releaseFromQuarantine(item.id)}
                  className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded transition-colors"
                >
                  Libérer
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HanumanImmuneInterface;
