import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>, <PERSON>, <PERSON>, <PERSON>f<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
  Clock, Calendar, Zap, Activity, TrendingUp,
  Thermometer, Droplets, Wind, Eye, Settings,
  <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RefreshCw
} from 'lucide-react';

// Types pour l'interface de cycles naturels
interface SeasonalData {
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  name: string;
  startDate: Date;
  endDate: Date;
  dayLength: number; // en heures
  temperature: {
    min: number;
    max: number;
    average: number;
  };
  humidity: number;
  energyLevel: number; // 0-1
  characteristics: string[];
  color: string;
  icon: React.ComponentType;
}

interface LunarPhase {
  phase: 'new' | 'waxing_crescent' | 'first_quarter' | 'waxing_gibbous' |
         'full' | 'waning_gibbous' | 'last_quarter' | 'waning_crescent';
  name: string;
  illumination: number; // 0-1
  date: Date;
  influence: {
    energy: number;
    creativity: number;
    focus: number;
    intuition: number;
  };
  recommendations: string[];
  color: string;
}

interface CircadianRhythm {
  hour: number;
  period: 'dawn' | 'morning' | 'midday' | 'afternoon' | 'evening' | 'night' | 'deep_night';
  energyLevel: number; // 0-1
  alertness: number; // 0-1
  creativity: number; // 0-1
  physicalPerformance: number; // 0-1
  optimalActivities: string[];
  aiPerformance: number; // Performance optimale de l'IA
}

interface EnergyOptimization {
  timestamp: Date;
  currentEfficiency: number;
  optimalEfficiency: number;
  factors: {
    seasonal: number;
    lunar: number;
    circadian: number;
    weather: number;
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  };
  nextOptimalWindow: {
    start: Date;
    end: Date;
    efficiency: number;
    activities: string[];
  };
}

interface WeatherData {
  temperature: number;
  humidity: number;
  pressure: number;
  windSpeed: number;
  cloudCover: number;
  uvIndex: number;
  visibility: number;
  condition: string;
  impact: {
    energy: number;
    mood: number;
    performance: number;
  };
}

interface NaturalCycleInsight {
  id: string;
  type: 'seasonal' | 'lunar' | 'circadian' | 'weather' | 'combined';
  title: string;
  description: string;
  impact: number; // 0-1
  timeframe: string;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

const HanumanNaturalCyclesInterface = ({ darkMode = true }) => {
  const [currentSeason, setCurrentSeason] = useState<SeasonalData | null>(null);
  const [lunarPhase, setLunarPhase] = useState<LunarPhase | null>(null);
  const [circadianData, setCircadianData] = useState<CircadianRhythm[]>([]);
  const [currentCircadian, setCurrentCircadian] = useState<CircadianRhythm | null>(null);
  const [energyOptimization, setEnergyOptimization] = useState<EnergyOptimization | null>(null);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [insights, setInsights] = useState<NaturalCycleInsight[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [autoSync, setAutoSync] = useState(true);
  const [selectedView, setSelectedView] = useState<'overview' | 'seasonal' | 'lunar' | 'circadian' | 'energy'>('overview');
  const wsRef = useRef<WebSocket | null>(null);
  const chartRef = useRef<HTMLCanvasElement>(null);

  // Données saisonnières simulées
  useEffect(() => {
    const seasons: SeasonalData[] = [
      {
        season: 'spring',
        name: 'Printemps',
        startDate: new Date(2024, 2, 20), // 20 mars
        endDate: new Date(2024, 5, 20), // 20 juin
        dayLength: 12.5,
        temperature: { min: 8, max: 18, average: 13 },
        humidity: 65,
        energyLevel: 0.75,
        characteristics: ['Renouveau', 'Croissance', 'Créativité', 'Nouveaux projets'],
        color: '#10B981',
        icon: Flower
      },
      {
        season: 'summer',
        name: 'Été',
        startDate: new Date(2024, 5, 20), // 20 juin
        endDate: new Date(2024, 8, 22), // 22 septembre
        dayLength: 15.5,
        temperature: { min: 18, max: 28, average: 23 },
        humidity: 55,
        energyLevel: 0.95,
        characteristics: ['Énergie maximale', 'Action', 'Expansion', 'Réalisation'],
        color: '#F59E0B',
        icon: Sun
      },
      {
        season: 'autumn',
        name: 'Automne',
        startDate: new Date(2024, 8, 22), // 22 septembre
        endDate: new Date(2024, 11, 21), // 21 décembre
        dayLength: 10.5,
        temperature: { min: 5, max: 15, average: 10 },
        humidity: 70,
        energyLevel: 0.60,
        characteristics: ['Récolte', 'Réflexion', 'Consolidation', 'Préparation'],
        color: '#DC2626',
        icon: Leaf
      },
      {
        season: 'winter',
        name: 'Hiver',
        startDate: new Date(2024, 11, 21), // 21 décembre
        endDate: new Date(2025, 2, 20), // 20 mars
        dayLength: 8.5,
        temperature: { min: -2, max: 8, average: 3 },
        humidity: 75,
        energyLevel: 0.40,
        characteristics: ['Repos', 'Introspection', 'Planification', 'Régénération'],
        color: '#3B82F6',
        icon: Snowflake
      }
    ];

    // Déterminer la saison actuelle
    const now = new Date();
    const currentSeasonData = seasons.find(season =>
      now >= season.startDate && now <= season.endDate
    ) || seasons[1]; // Défaut été

    setCurrentSeason(currentSeasonData);
  }, []);

  // Données lunaires simulées
  useEffect(() => {
    const lunarPhases: LunarPhase[] = [
      {
        phase: 'new',
        name: 'Nouvelle Lune',
        illumination: 0,
        date: new Date(),
        influence: { energy: 0.3, creativity: 0.8, focus: 0.9, intuition: 0.95 },
        recommendations: [
          'Planification et intention',
          'Nouveaux projets',
          'Méditation profonde',
          'Nettoyage énergétique'
        ],
        color: '#1F2937'
      },
      {
        phase: 'waxing_crescent',
        name: 'Premier Croissant',
        illumination: 0.25,
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        influence: { energy: 0.5, creativity: 0.7, focus: 0.8, intuition: 0.8 },
        recommendations: [
          'Mise en action des projets',
          'Apprentissage',
          'Construction',
          'Développement'
        ],
        color: '#374151'
      },
      {
        phase: 'first_quarter',
        name: 'Premier Quartier',
        illumination: 0.5,
        date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        influence: { energy: 0.7, creativity: 0.6, focus: 0.7, intuition: 0.6 },
        recommendations: [
          'Prise de décisions',
          'Résolution de problèmes',
          'Ajustements',
          'Persévérance'
        ],
        color: '#6B7280'
      },
      {
        phase: 'full',
        name: 'Pleine Lune',
        illumination: 1.0,
        date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
        influence: { energy: 0.95, creativity: 0.95, focus: 0.4, intuition: 0.95 },
        recommendations: [
          'Culmination des projets',
          'Célébration',
          'Libération',
          'Transformation'
        ],
        color: '#F3F4F6'
      }
    ];

    setLunarPhase(lunarPhases[0]); // Phase actuelle simulée
  }, []);

  // Données circadiennes
  useEffect(() => {
    const circadianCycle: CircadianRhythm[] = [
      { hour: 6, period: 'dawn', energyLevel: 0.3, alertness: 0.4, creativity: 0.6, physicalPerformance: 0.3, optimalActivities: ['Méditation', 'Planification'], aiPerformance: 0.5 },
      { hour: 8, period: 'morning', energyLevel: 0.7, alertness: 0.8, creativity: 0.7, physicalPerformance: 0.6, optimalActivities: ['Tâches complexes', 'Apprentissage'], aiPerformance: 0.85 },
      { hour: 10, period: 'morning', energyLevel: 0.9, alertness: 0.95, creativity: 0.8, physicalPerformance: 0.8, optimalActivities: ['Résolution de problèmes', 'Créativité'], aiPerformance: 0.95 },
      { hour: 12, period: 'midday', energyLevel: 0.85, alertness: 0.9, creativity: 0.6, physicalPerformance: 0.9, optimalActivities: ['Exécution', 'Communication'], aiPerformance: 0.9 },
      { hour: 14, period: 'afternoon', energyLevel: 0.6, alertness: 0.6, creativity: 0.5, physicalPerformance: 0.7, optimalActivities: ['Tâches routinières', 'Organisation'], aiPerformance: 0.7 },
      { hour: 16, period: 'afternoon', energyLevel: 0.75, alertness: 0.8, creativity: 0.7, physicalPerformance: 0.8, optimalActivities: ['Collaboration', 'Révision'], aiPerformance: 0.8 },
      { hour: 18, period: 'evening', energyLevel: 0.7, alertness: 0.7, creativity: 0.8, physicalPerformance: 0.6, optimalActivities: ['Synthèse', 'Réflexion'], aiPerformance: 0.75 },
      { hour: 20, period: 'evening', energyLevel: 0.5, alertness: 0.5, creativity: 0.9, physicalPerformance: 0.4, optimalActivities: ['Créativité libre', 'Inspiration'], aiPerformance: 0.6 },
      { hour: 22, period: 'night', energyLevel: 0.3, alertness: 0.3, creativity: 0.7, physicalPerformance: 0.2, optimalActivities: ['Détente', 'Intégration'], aiPerformance: 0.4 },
      { hour: 0, period: 'deep_night', energyLevel: 0.1, alertness: 0.1, creativity: 0.3, physicalPerformance: 0.1, optimalActivities: ['Repos', 'Régénération'], aiPerformance: 0.2 }
    ];

    setCircadianData(circadianCycle);

    // Déterminer le cycle actuel
    const currentHour = new Date().getHours();
    const current = circadianCycle.find(cycle =>
      Math.abs(cycle.hour - currentHour) <= 1
    ) || circadianCycle[0];

    setCurrentCircadian(current);
  }, []);

  // Données météorologiques simulées
  useEffect(() => {
    const mockWeather: WeatherData = {
      temperature: 22,
      humidity: 65,
      pressure: 1013,
      windSpeed: 15,
      cloudCover: 30,
      uvIndex: 6,
      visibility: 10,
      condition: 'Partiellement nuageux',
      impact: {
        energy: 0.8,
        mood: 0.75,
        performance: 0.85
      }
    };

    setWeatherData(mockWeather);
  }, []);

  // Optimisation énergétique
  useEffect(() => {
    if (currentSeason && lunarPhase && currentCircadian && weatherData) {
      const optimization: EnergyOptimization = {
        timestamp: new Date(),
        currentEfficiency: 0.78,
        optimalEfficiency: 0.92,
        factors: {
          seasonal: currentSeason.energyLevel,
          lunar: lunarPhase.influence.energy,
          circadian: currentCircadian.aiPerformance,
          weather: weatherData.impact.energy
        },
        recommendations: {
          immediate: [
            'Profiter de l\'énergie lunaire croissante',
            'Optimiser selon le rythme circadien actuel',
            'Adapter aux conditions météorologiques'
          ],
          shortTerm: [
            'Planifier les tâches importantes pour demain matin',
            'Préparer la synchronisation avec la prochaine phase lunaire',
            'Ajuster les paramètres selon la saison'
          ],
          longTerm: [
            'Développer des patterns saisonniers adaptatifs',
            'Intégrer les cycles lunaires dans la planification',
            'Créer des profils circadiens personnalisés'
          ]
        },
        nextOptimalWindow: {
          start: new Date(Date.now() + 10 * 60 * 60 * 1000), // Dans 10h
          end: new Date(Date.now() + 14 * 60 * 60 * 1000), // Dans 14h
          efficiency: 0.95,
          activities: ['Tâches complexes', 'Créativité', 'Résolution de problèmes']
        }
      };

      setEnergyOptimization(optimization);
    }
  }, [currentSeason, lunarPhase, currentCircadian, weatherData]);

  // Génération d'insights
  useEffect(() => {
    const generatedInsights: NaturalCycleInsight[] = [
      {
        id: 'insight-001',
        type: 'combined',
        title: 'Synergie Optimale Détectée',
        description: 'La combinaison actuelle saison-lune-circadien offre une fenêtre d\'efficacité exceptionnelle pour les tâches créatives.',
        impact: 0.92,
        timeframe: 'Prochaines 4 heures',
        actionable: true,
        priority: 'high'
      },
      {
        id: 'insight-002',
        type: 'lunar',
        title: 'Influence Lunaire Croissante',
        description: 'La phase lunaire actuelle favorise l\'intuition et la créativité. Idéal pour l\'innovation et les nouveaux concepts.',
        impact: 0.78,
        timeframe: 'Prochains 3 jours',
        actionable: true,
        priority: 'medium'
      },
      {
        id: 'insight-003',
        type: 'seasonal',
        title: 'Transition Saisonnière',
        description: 'L\'énergie saisonnière est en phase d\'expansion. Moment propice pour lancer de nouveaux projets.',
        impact: 0.85,
        timeframe: 'Prochaines 2 semaines',
        actionable: true,
        priority: 'high'
      },
      {
        id: 'insight-004',
        type: 'circadian',
        title: 'Pic de Performance Matinal',
        description: 'Le rythme circadien indique une performance optimale entre 9h et 11h pour les tâches analytiques.',
        impact: 0.88,
        timeframe: 'Quotidien',
        actionable: true,
        priority: 'medium'
      }
    ];

    setInsights(generatedInsights);
  }, []);

  // Connexion WebSocket pour synchronisation avec les agents
  useEffect(() => {
    if (autoSync) {
      // Simulation de connexion avec les agents web-research et evolution
      wsRef.current = new WebSocket('ws://localhost:8080/natural-cycles');

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'weather_update':
            setWeatherData(data.weather);
            break;
          case 'seasonal_insight':
            setInsights(prev => [...prev, data.insight]);
            break;
          case 'energy_optimization':
            setEnergyOptimization(data.optimization);
            break;
          case 'circadian_adjustment':
            setCurrentCircadian(data.circadian);
            break;
        }
      };

      // Envoyer des requêtes aux agents
      const sendAgentRequests = () => {
        // Requête à l'agent web-research pour données météo
        wsRef.current?.send(JSON.stringify({
          type: 'weather_request',
          agent: 'web-research',
          query: 'current weather conditions and forecast',
          location: 'current'
        }));

        // Requête à l'agent evolution pour optimisation
        wsRef.current?.send(JSON.stringify({
          type: 'optimization_request',
          agent: 'evolution',
          context: {
            season: currentSeason?.season,
            lunar: lunarPhase?.phase,
            circadian: currentCircadian?.period
          }
        }));
      };

      // Envoyer les requêtes initiales
      setTimeout(sendAgentRequests, 1000);

      return () => {
        wsRef.current?.close();
      };
    }
  }, [autoSync, currentSeason, lunarPhase, currentCircadian]);

  // Mise à jour de l'heure
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'low': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl">
              <Leaf className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                Cycles Naturels Hanuman
              </h1>
              <p className="text-gray-400 mt-1">
                Synchronisation avec les rythmes de la nature et optimisation énergétique
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-400">Temps Naturel</p>
              <p className="font-mono text-lg">{formatTime(currentTime)}</p>
              <p className="text-xs text-gray-500">{formatDate(currentTime)}</p>
            </div>
            <button
              onClick={() => setAutoSync(!autoSync)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                autoSync
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }`}
            >
              {autoSync ? <RefreshCw className="w-4 h-4" /> : <Settings className="w-4 h-4" />}
              <span>{autoSync ? 'Sync Auto' : 'Manuel'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Navigation des vues */}
      <div className="mb-8">
        <div className="flex space-x-2 bg-gray-800 p-1 rounded-lg">
          {[
            { key: 'overview', label: 'Vue d\'ensemble', icon: Eye },
            { key: 'seasonal', label: 'Saisonnier', icon: Leaf },
            { key: 'lunar', label: 'Lunaire', icon: Moon },
            { key: 'circadian', label: 'Circadien', icon: Clock },
            { key: 'energy', label: 'Énergie', icon: Zap }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSelectedView(key as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                selectedView === key
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Vue d'ensemble */}
      {selectedView === 'overview' && (
        <div className="space-y-8">
          {/* Statut global des cycles */}
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <Activity className="w-5 h-5 mr-2 text-green-400" />
              Synchronisation des Cycles Naturels
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Saison actuelle */}
              {currentSeason && (
                <div className="text-center">
                  <div className="p-4 rounded-lg bg-gradient-to-br from-green-500/20 to-blue-500/20 border border-green-500/30 mb-3">
                    <currentSeason.icon className="w-8 h-8 mx-auto mb-2" style={{ color: currentSeason.color }} />
                    <h4 className="font-medium">{currentSeason.name}</h4>
                    <p className="text-sm text-gray-400">Énergie: {(currentSeason.energyLevel * 100).toFixed(0)}%</p>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="h-2 rounded-full transition-all duration-1000"
                      style={{
                        width: `${currentSeason.energyLevel * 100}%`,
                        backgroundColor: currentSeason.color
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Phase lunaire */}
              {lunarPhase && (
                <div className="text-center">
                  <div className="p-4 rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-purple-500/30 mb-3">
                    <Moon className="w-8 h-8 mx-auto mb-2" style={{ color: lunarPhase.color }} />
                    <h4 className="font-medium">{lunarPhase.name}</h4>
                    <p className="text-sm text-gray-400">Illumination: {(lunarPhase.illumination * 100).toFixed(0)}%</p>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-purple-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${lunarPhase.illumination * 100}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Rythme circadien */}
              {currentCircadian && (
                <div className="text-center">
                  <div className="p-4 rounded-lg bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-500/30 mb-3">
                    <Clock className="w-8 h-8 mx-auto mb-2 text-orange-400" />
                    <h4 className="font-medium capitalize">{currentCircadian.period}</h4>
                    <p className="text-sm text-gray-400">IA: {(currentCircadian.aiPerformance * 100).toFixed(0)}%</p>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-orange-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${currentCircadian.aiPerformance * 100}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Météo */}
              {weatherData && (
                <div className="text-center">
                  <div className="p-4 rounded-lg bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-500/30 mb-3">
                    <Thermometer className="w-8 h-8 mx-auto mb-2 text-blue-400" />
                    <h4 className="font-medium">{weatherData.condition}</h4>
                    <p className="text-sm text-gray-400">{weatherData.temperature}°C</p>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${weatherData.impact.energy * 100}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Optimisation énergétique globale */}
          {energyOptimization && (
            <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
              <h3 className="text-xl font-semibold mb-6 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-400" />
                Optimisation Énergétique Globale
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-gray-400">Efficacité Actuelle</span>
                    <span className="text-2xl font-bold text-yellow-400">
                      {(energyOptimization.currentEfficiency * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-600 rounded-full h-4 mb-4">
                    <div
                      className="bg-gradient-to-r from-yellow-500 to-orange-500 h-4 rounded-full transition-all duration-1000"
                      style={{ width: `${energyOptimization.currentEfficiency * 100}%` }}
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Facteur Saisonnier:</span>
                      <span className="text-green-400">{(energyOptimization.factors.seasonal * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Facteur Lunaire:</span>
                      <span className="text-purple-400">{(energyOptimization.factors.lunar * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Facteur Circadien:</span>
                      <span className="text-orange-400">{(energyOptimization.factors.circadian * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Facteur Météo:</span>
                      <span className="text-blue-400">{(energyOptimization.factors.weather * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-blue-400 mb-3">Prochaine Fenêtre Optimale</h4>
                  <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <p className="text-sm mb-2">
                      <span className="font-medium">Début:</span> {energyOptimization.nextOptimalWindow.start.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                    </p>
                    <p className="text-sm mb-2">
                      <span className="font-medium">Fin:</span> {energyOptimization.nextOptimalWindow.end.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                    </p>
                    <p className="text-sm mb-3">
                      <span className="font-medium">Efficacité:</span> <span className="text-blue-400">{(energyOptimization.nextOptimalWindow.efficiency * 100).toFixed(0)}%</span>
                    </p>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">Activités recommandées:</p>
                      <div className="flex flex-wrap gap-1">
                        {energyOptimization.nextOptimalWindow.activities.map((activity, index) => (
                          <span
                            key={index}
                            className="text-xs px-2 py-1 bg-blue-500/20 text-blue-400 rounded"
                          >
                            {activity}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Insights et recommandations */}
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-purple-400" />
              Insights des Cycles Naturels
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {insights.map((insight) => (
                <div
                  key={insight.id}
                  className={`p-4 rounded-lg border ${getPriorityColor(insight.priority)}`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="font-medium">{insight.title}</h4>
                    <span className={`text-xs px-2 py-1 rounded font-medium ${getPriorityColor(insight.priority)}`}>
                      {insight.priority}
                    </span>
                  </div>

                  <p className="text-sm text-gray-300 mb-3">{insight.description}</p>

                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-400">{insight.timeframe}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-400">Impact:</span>
                      <div className="w-16 bg-gray-600 rounded-full h-1">
                        <div
                          className="bg-purple-500 h-1 rounded-full"
                          style={{ width: `${insight.impact * 100}%` }}
                        />
                      </div>
                      <span className="text-purple-400">{(insight.impact * 100).toFixed(0)}%</span>
                    </div>
                  </div>

                  {insight.actionable && (
                    <div className="mt-3 pt-3 border-t border-gray-600">
                      <span className="text-xs text-green-400 flex items-center">
                        <Activity className="w-3 h-3 mr-1" />
                        Action recommandée
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recommandations par période */}
          {energyOptimization && (
            <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
              <h3 className="text-xl font-semibold mb-6 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-blue-400" />
                Recommandations Temporelles
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-green-400 mb-3 flex items-center">
                    <Zap className="w-4 h-4 mr-2" />
                    Actions Immédiates
                  </h4>
                  <ul className="space-y-2">
                    {energyOptimization.recommendations.immediate.map((rec, index) => (
                      <li key={index} className="text-sm text-gray-300 flex items-start">
                        <span className="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-yellow-400 mb-3 flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    Court Terme
                  </h4>
                  <ul className="space-y-2">
                    {energyOptimization.recommendations.shortTerm.map((rec, index) => (
                      <li key={index} className="text-sm text-gray-300 flex items-start">
                        <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-blue-400 mb-3 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Long Terme
                  </h4>
                  <ul className="space-y-2">
                    {energyOptimization.recommendations.longTerm.map((rec, index) => (
                      <li key={index} className="text-sm text-gray-300 flex items-start">
                        <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Vue détaillée saisonnière */}
      {selectedView === 'seasonal' && currentSeason && (
        <div className="space-y-8">
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <currentSeason.icon className="w-5 h-5 mr-2" style={{ color: currentSeason.color }} />
              Analyse Saisonnière - {currentSeason.name}
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-4">Caractéristiques de la Saison</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Durée du jour:</span>
                    <span className="font-medium">{currentSeason.dayLength}h</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Température moyenne:</span>
                    <span className="font-medium">{currentSeason.temperature.average}°C</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Humidité:</span>
                    <span className="font-medium">{currentSeason.humidity}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Niveau d'énergie:</span>
                    <span className="font-medium" style={{ color: currentSeason.color }}>
                      {(currentSeason.energyLevel * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Caractéristiques Énergétiques</h4>
                <div className="space-y-2">
                  {currentSeason.characteristics.map((char, index) => (
                    <div
                      key={index}
                      className="px-3 py-2 rounded-lg border"
                      style={{
                        borderColor: currentSeason.color + '30',
                        backgroundColor: currentSeason.color + '10'
                      }}
                    >
                      <span className="text-sm" style={{ color: currentSeason.color }}>
                        {char}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vue détaillée lunaire */}
      {selectedView === 'lunar' && lunarPhase && (
        <div className="space-y-8">
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <Moon className="w-5 h-5 mr-2" style={{ color: lunarPhase.color }} />
              Analyse Lunaire - {lunarPhase.name}
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-4">Influences Lunaires</h4>
                <div className="space-y-4">
                  {Object.entries(lunarPhase.influence).map(([key, value]) => (
                    <div key={key}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400 capitalize">{key}:</span>
                        <span className="font-medium text-purple-400">{(value * 100).toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${value * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Recommandations Lunaires</h4>
                <div className="space-y-2">
                  {lunarPhase.recommendations.map((rec, index) => (
                    <div
                      key={index}
                      className="px-3 py-2 rounded-lg bg-purple-500/10 border border-purple-500/20"
                    >
                      <span className="text-sm text-purple-400">{rec}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vue détaillée circadienne */}
      {selectedView === 'circadian' && (
        <div className="space-y-8">
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <Clock className="w-5 h-5 mr-2 text-orange-400" />
              Analyse Circadienne - Rythme de 24h
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-4">Cycle Actuel</h4>
                {currentCircadian && (
                  <div className="space-y-4">
                    <div className="p-4 rounded-lg bg-orange-500/10 border border-orange-500/20">
                      <h5 className="font-medium text-orange-400 capitalize mb-2">{currentCircadian.period}</h5>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Énergie:</span>
                          <span className="text-orange-400">{(currentCircadian.energyLevel * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Vigilance:</span>
                          <span className="text-orange-400">{(currentCircadian.alertness * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Créativité:</span>
                          <span className="text-orange-400">{(currentCircadian.creativity * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">Performance IA:</span>
                          <span className="text-orange-400">{(currentCircadian.aiPerformance * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h6 className="font-medium text-gray-400 mb-2">Activités Optimales</h6>
                      <div className="space-y-1">
                        {currentCircadian.optimalActivities.map((activity, index) => (
                          <div
                            key={index}
                            className="px-3 py-1 rounded bg-orange-500/20 text-orange-400 text-sm"
                          >
                            {activity}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h4 className="font-medium mb-4">Cycle Complet (24h)</h4>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {circadianData.map((cycle, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border transition-colors ${
                        currentCircadian?.hour === cycle.hour
                          ? 'bg-orange-500/20 border-orange-500/40'
                          : 'bg-gray-700/50 border-gray-600'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{cycle.hour}h - {cycle.period}</span>
                        <span className="text-sm text-orange-400">
                          IA: {(cycle.aiPerformance * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-1">
                        <div
                          className="bg-orange-500 h-1 rounded-full"
                          style={{ width: `${cycle.aiPerformance * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vue détaillée énergétique */}
      {selectedView === 'energy' && energyOptimization && (
        <div className="space-y-8">
          <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-yellow-400" />
              Optimisation Énergétique Avancée
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <h4 className="font-medium mb-4">Analyse des Facteurs</h4>
                <div className="space-y-4">
                  {Object.entries(energyOptimization.factors).map(([factor, value]) => (
                    <div key={factor}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400 capitalize">{factor}:</span>
                        <span className="font-medium text-yellow-400">{(value * 100).toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-1000 ${
                            factor === 'seasonal' ? 'bg-green-500' :
                            factor === 'lunar' ? 'bg-purple-500' :
                            factor === 'circadian' ? 'bg-orange-500' :
                            'bg-blue-500'
                          }`}
                          style={{ width: `${value * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Efficacité Globale</h4>
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        className="text-gray-600"
                        stroke="currentColor"
                        strokeWidth="3"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                      <path
                        className="text-yellow-400"
                        stroke="currentColor"
                        strokeWidth="3"
                        fill="none"
                        strokeDasharray={`${energyOptimization.currentEfficiency * 100}, 100`}
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-2xl font-bold text-yellow-400">
                        {(energyOptimization.currentEfficiency * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-400">Efficacité Actuelle</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Optimal: {(energyOptimization.optimalEfficiency * 100).toFixed(0)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HanumanNaturalCyclesInterface;
