/**
 * 🧪 Script de test pour l'API Chat Hanuman
 * Teste les différentes catégories de réponses
 */

const testMessages = [
  // Tests de salutations
  {
    category: 'Salutations',
    messages: [
      'Bon<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>e, comment allez-vous ?',
      'Salut ! Peux-tu te présenter ?'
    ]
  },
  
  // Tests bien-être
  {
    category: 'Bien-être',
    messages: [
      'Je cherche une retraite de méditation',
      'Comment gérer mon stress ?',
      'Aide-moi avec l\'anxiété',
      'Retraite spirituelle en Inde'
    ]
  },
  
  // Tests techniques
  {
    category: 'Technique',
    messages: [
      'Comment créer une API REST ?',
      'Explique-moi React',
      'Architecture microservices',
      'Optimisation de code'
    ]
  },
  
  // Tests créatifs
  {
    category: 'Créativité',
    messages: [
      'Créons une interface innovante',
      'Inspire-moi pour un design',
      'Idées créatives pour une app',
      'Design thinking'
    ]
  },
  
  // Tests retraites
  {
    category: 'Retraites',
    messages: [
      'Destinations retraite à Bali',
      'Voyage spirituel en France',
      'Séjour yoga en Inde',
      'Retraite détox digitale'
    ]
  },
  
  // Tests généraux
  {
    category: 'Général',
    messages: [
      'Que penses-tu de la vie ?',
      'Raconte-moi une histoire',
      'Quel est le sens de l\'existence ?'
    ]
  }
];

async function testChatAPI() {
  console.log('🐒 ========================================');
  console.log('🧪 TEST API CHAT HANUMAN');
  console.log('🕉️ AUM HANUMATE NAMAHA');
  console.log('🐒 ========================================\n');

  const baseURL = 'http://localhost:3001';
  let totalTests = 0;
  let successfulTests = 0;

  for (const testGroup of testMessages) {
    console.log(`\n📋 === ${testGroup.category.toUpperCase()} ===`);
    
    for (const message of testGroup.messages) {
      totalTests++;
      console.log(`\n💬 Test: "${message}"`);
      
      try {
        const startTime = Date.now();
        
        const response = await fetch(`${baseURL}/api/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: message,
            timestamp: new Date().toISOString()
          })
        });

        const responseTime = Date.now() - startTime;

        if (response.ok) {
          const data = await response.json();
          successfulTests++;
          
          console.log(`✅ Succès (${responseTime}ms)`);
          console.log(`🤖 Réponse: ${data.response.substring(0, 100)}...`);
          console.log(`🧠 Agents: ${data.agents.map(a => a.emoji + a.name).join(', ')}`);
          console.log(`📊 Confiance: ${(data.confidence * 100).toFixed(1)}%`);
          console.log(`⏱️  Traitement: ${data.processingTime}ms`);
        } else {
          console.log(`❌ Erreur HTTP: ${response.status}`);
          const errorText = await response.text();
          console.log(`📝 Détail: ${errorText}`);
        }
        
      } catch (error) {
        console.log(`💥 Erreur: ${error.message}`);
      }
      
      // Pause entre les tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log('\n🐒 ========================================');
  console.log('📊 RÉSULTATS DES TESTS');
  console.log('🐒 ========================================');
  console.log(`✅ Tests réussis: ${successfulTests}/${totalTests}`);
  console.log(`📈 Taux de succès: ${((successfulTests/totalTests) * 100).toFixed(1)}%`);
  
  if (successfulTests === totalTests) {
    console.log('🎉 TOUS LES TESTS SONT PASSÉS !');
    console.log('🐒 Hanuman est prêt pour les conversations !');
  } else {
    console.log('⚠️  Certains tests ont échoué');
    console.log('🔧 Vérifiez la configuration de l\'API');
  }
  
  console.log('\n🕉️ AUM HANUMATE NAMAHA - Tests terminés ✨');
}

// Fonction pour tester une seule catégorie
async function testCategory(categoryName) {
  const testGroup = testMessages.find(group => 
    group.category.toLowerCase() === categoryName.toLowerCase()
  );
  
  if (!testGroup) {
    console.log(`❌ Catégorie "${categoryName}" non trouvée`);
    console.log('📋 Catégories disponibles:', testMessages.map(g => g.category).join(', '));
    return;
  }

  console.log(`🧪 Test spécifique: ${testGroup.category}`);
  
  for (const message of testGroup.messages) {
    console.log(`\n💬 "${message}"`);
    
    try {
      const response = await fetch('http://localhost:3001/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message, timestamp: new Date().toISOString() })
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`🤖 ${data.response}`);
        console.log(`🧠 Agents: ${data.agents.map(a => a.emoji).join('')}`);
      } else {
        console.log(`❌ Erreur: ${response.status}`);
      }
    } catch (error) {
      console.log(`💥 ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Fonction pour test interactif
async function interactiveTest() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('🐒 Mode interactif Hanuman');
  console.log('💬 Tapez vos messages (tapez "exit" pour quitter)\n');

  const askQuestion = () => {
    rl.question('Vous: ', async (message) => {
      if (message.toLowerCase() === 'exit') {
        console.log('🙏 Au revoir ! AUM HANUMATE NAMAHA');
        rl.close();
        return;
      }

      try {
        const response = await fetch('http://localhost:3001/api/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message, timestamp: new Date().toISOString() })
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`\n🐒 Hanuman: ${data.response}`);
          console.log(`🧠 Agents actifs: ${data.agents.map(a => a.emoji + a.name).join(', ')}\n`);
        } else {
          console.log(`❌ Erreur API: ${response.status}\n`);
        }
      } catch (error) {
        console.log(`💥 Erreur: ${error.message}\n`);
      }

      askQuestion();
    });
  };

  askQuestion();
}

// Gestion des arguments de ligne de commande
const args = process.argv.slice(2);

if (args.length === 0) {
  testChatAPI();
} else if (args[0] === 'interactive') {
  interactiveTest();
} else if (args[0] === 'category' && args[1]) {
  testCategory(args[1]);
} else {
  console.log('🐒 Usage:');
  console.log('  node test-chat-api.js                    # Tests complets');
  console.log('  node test-chat-api.js interactive         # Mode interactif');
  console.log('  node test-chat-api.js category [nom]      # Test catégorie spécifique');
  console.log('\n📋 Catégories disponibles:', testMessages.map(g => g.category).join(', '));
}
