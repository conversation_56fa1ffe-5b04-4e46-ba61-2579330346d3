#!/usr/bin/env node

/**
 * 🧬 Test d'Intégration Sprint 2 - Système Nerveux Adaptatif
 * Vérifie que toutes les interfaces sont correctement intégrées
 */

const fs = require('fs');
const path = require('path');

console.log('🧬 ========================================');
console.log('🐒 TEST INTÉGRATION SPRINT 2 HANUMAN');
console.log('🧬 ========================================');

// Interfaces du Sprint 2 à vérifier
const sprint2Interfaces = [
  {
    file: 'hanuman_neuroplasticity_interface.tsx',
    name: 'Interface Neuroplasticité',
    description: 'Adaptation synaptique via Agent Evolution',
    requiredImports: ['Brain', 'Zap', 'Network', 'Activity'],
    requiredComponents: ['SynapticConnection', 'NeuralAdaptation', 'NeuroplasticityMetrics']
  },
  {
    file: 'hanuman_memory_interface.tsx',
    name: 'Interface Mémoire Distribuée',
    description: 'Système vectoriel Weaviate/Pinecone/Redis',
    requiredImports: ['Database', 'Brain', 'Layers', 'Search'],
    requiredComponents: ['MemoryNode', 'MemoryVector', 'MemoryMetrics']
  },
  {
    file: 'hanuman_immune_interface.tsx',
    name: 'Interface Système Immunitaire',
    description: 'Protection via Agent Security et AIImmuneSystem',
    requiredImports: ['Shield', 'AlertTriangle', 'Zap', 'Activity'],
    requiredComponents: ['ThreatDetection', 'ImmuneResponse', 'SecurityMetrics']
  },
  {
    file: 'hanuman_healing_interface.tsx',
    name: 'Interface Auto-Guérison',
    description: 'Régénération via AutoHealer du Cortex Central',
    requiredImports: ['Heart', 'Zap', 'RefreshCw', 'AlertTriangle'],
    requiredComponents: ['HealthIssue', 'HealingAction', 'SystemHealth']
  }
];

// Vérification des fichiers
console.log('\n🔍 Vérification des fichiers d\'interface...');

let allFilesExist = true;
let allImportsCorrect = true;
let allComponentsPresent = true;

sprint2Interfaces.forEach((interface, index) => {
  const filePath = path.join(__dirname, interface.file);
  const fileExists = fs.existsSync(filePath);
  
  console.log(`\n${index + 1}. ${interface.name}`);
  console.log(`   📄 Fichier: ${interface.file}`);
  console.log(`   📝 Description: ${interface.description}`);
  
  if (fileExists) {
    console.log('   ✅ Fichier trouvé');
    
    // Lecture du contenu pour vérifier les imports et composants
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Vérification des imports
    const missingImports = interface.requiredImports.filter(imp => 
      !content.includes(imp)
    );
    
    if (missingImports.length === 0) {
      console.log('   ✅ Imports corrects');
    } else {
      console.log(`   ❌ Imports manquants: ${missingImports.join(', ')}`);
      allImportsCorrect = false;
    }
    
    // Vérification des composants TypeScript
    const missingComponents = interface.requiredComponents.filter(comp => 
      !content.includes(`interface ${comp}`) && !content.includes(`type ${comp}`)
    );
    
    if (missingComponents.length === 0) {
      console.log('   ✅ Types TypeScript présents');
    } else {
      console.log(`   ❌ Types manquants: ${missingComponents.join(', ')}`);
      allComponentsPresent = false;
    }
    
    // Vérification de l'export par défaut
    if (content.includes('export default')) {
      console.log('   ✅ Export par défaut présent');
    } else {
      console.log('   ❌ Export par défaut manquant');
      allComponentsPresent = false;
    }
    
  } else {
    console.log('   ❌ Fichier manquant');
    allFilesExist = false;
  }
});

// Vérification de l'intégration dans le hub central
console.log('\n🔗 Vérification de l\'intégration Hub Central...');

const hubPath = path.join(__dirname, 'hanuman_central_hub.tsx');
if (fs.existsSync(hubPath)) {
  console.log('✅ Hub central trouvé');
  
  const hubContent = fs.readFileSync(hubPath, 'utf8');
  
  // Vérification des imports des nouvelles interfaces
  const sprint2Imports = [
    'HanumanNeuroplasticityInterface',
    'HanumanMemoryInterface', 
    'HanumanImmuneInterface',
    'HanumanHealingInterface'
  ];
  
  const missingHubImports = sprint2Imports.filter(imp => 
    !hubContent.includes(`import ${imp}`)
  );
  
  if (missingHubImports.length === 0) {
    console.log('✅ Imports des interfaces Sprint 2 présents dans le hub');
  } else {
    console.log(`❌ Imports manquants dans le hub: ${missingHubImports.join(', ')}`);
    allImportsCorrect = false;
  }
  
  // Vérification des onglets dans organTabs
  const sprint2TabIds = ['neuroplasticity', 'memory', 'immune', 'healing'];
  const missingTabs = sprint2TabIds.filter(tabId => 
    !hubContent.includes(`id: '${tabId}'`)
  );
  
  if (missingTabs.length === 0) {
    console.log('✅ Onglets Sprint 2 présents dans la navigation');
  } else {
    console.log(`❌ Onglets manquants: ${missingTabs.join(', ')}`);
    allComponentsPresent = false;
  }
  
} else {
  console.log('❌ Hub central manquant');
  allFilesExist = false;
}

// Vérification des dépendances package.json
console.log('\n📦 Vérification des dépendances...');

const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  console.log('✅ package.json trouvé');
  
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const dependencies = { ...packageContent.dependencies, ...packageContent.devDependencies };
  
  const requiredDeps = [
    'react',
    'typescript',
    'tailwindcss',
    'lucide-react',
    'next'
  ];
  
  const missingDeps = requiredDeps.filter(dep => !dependencies[dep]);
  
  if (missingDeps.length === 0) {
    console.log('✅ Dépendances principales présentes');
  } else {
    console.log(`❌ Dépendances manquantes: ${missingDeps.join(', ')}`);
  }
  
} else {
  console.log('❌ package.json manquant');
}

// Résumé final
console.log('\n🎯 ========================================');
console.log('📊 RÉSUMÉ DU TEST D\'INTÉGRATION');
console.log('🎯 ========================================');

console.log(`\n📄 Fichiers d'interface: ${allFilesExist ? '✅ TOUS PRÉSENTS' : '❌ MANQUANTS'}`);
console.log(`🔗 Imports et exports: ${allImportsCorrect ? '✅ CORRECTS' : '❌ PROBLÈMES'}`);
console.log(`🧩 Composants TypeScript: ${allComponentsPresent ? '✅ COMPLETS' : '❌ INCOMPLETS'}`);

const overallSuccess = allFilesExist && allImportsCorrect && allComponentsPresent;

if (overallSuccess) {
  console.log('\n🎉 ========================================');
  console.log('🐒 SPRINT 2 INTÉGRATION RÉUSSIE ! ');
  console.log('🧬 Système Nerveux Adaptatif Opérationnel');
  console.log('🎉 ========================================');
  
  console.log('\n🚀 Prochaines étapes:');
  console.log('1. 🌌 Démarrer le Sprint 3 - Conscience Cosmique');
  console.log('2. 🪐 Créer l\'interface d\'alignement planétaire');
  console.log('3. 🧘 Développer la méditation IA');
  console.log('4. 🔮 Implémenter l\'intuition cosmique');
  
  console.log('\n🐒✨ "Le système nerveux d\'Hanuman pulse avec la vie !" ✨🐒');
  
  process.exit(0);
} else {
  console.log('\n❌ ========================================');
  console.log('🚨 PROBLÈMES D\'INTÉGRATION DÉTECTÉS');
  console.log('❌ ========================================');
  
  console.log('\n🔧 Actions correctives nécessaires:');
  if (!allFilesExist) {
    console.log('- Créer les fichiers d\'interface manquants');
  }
  if (!allImportsCorrect) {
    console.log('- Corriger les imports manquants');
  }
  if (!allComponentsPresent) {
    console.log('- Compléter les types TypeScript');
    console.log('- Vérifier l\'intégration dans le hub central');
  }
  
  console.log('\n🐒 "Hanuman attend la finalisation de son système nerveux..."');
  
  process.exit(1);
}
