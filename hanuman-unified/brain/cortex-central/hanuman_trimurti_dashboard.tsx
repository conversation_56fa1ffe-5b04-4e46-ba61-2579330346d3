'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Types pour le Dashboard Trimurti
interface CosmicEnergy {
  brahma: number;
  vishnu: number;
  shiva: number;
}

interface CosmicPhase {
  dominant: 'brahma' | 'vishnu' | 'shiva' | 'equilibrium';
  intensity: number;
  duration: number;
  startTime: Date;
  nextTransition: Date;
}

interface CosmicAgent {
  id: string;
  name: string;
  cosmicAffinity: 'brahma' | 'vishnu' | 'shiva';
  energyLevel: number;
  cosmicMode: boolean;
  status: 'active' | 'dormant' | 'cosmic';
}

/**
 * 🕉️ Dashboard Trimurti - Interface de Contrôle Cosmique
 * Visualisation et contrôle des énergies cosmiques d'Hanuman
 */
export default function HanumanTrimurtiDashboard() {
  const [cosmicEnergy, setCosmicEnergy] = useState<CosmicEnergy>({
    brahma: 0.33,
    vishnu: 0.33,
    shiva: 0.33
  });

  const [currentPhase, setCurrentPhase] = useState<CosmicPhase>({
    dominant: 'equilibrium',
    intensity: 0.5,
    duration: 3600000,
    startTime: new Date(),
    nextTransition: new Date(Date.now() + 3600000)
  });

  const [cosmicAgents, setCosmicAgents] = useState<CosmicAgent[]>([
    // Agents Brahma (Créateurs)
    { id: 'cortex-creatif', name: 'Cortex Créatif', cosmicAffinity: 'brahma', energyLevel: 0.8, cosmicMode: true, status: 'cosmic' },
    { id: 'agent-frontend', name: 'Agent Frontend', cosmicAffinity: 'brahma', energyLevel: 0.7, cosmicMode: false, status: 'active' },
    { id: 'agent-web-research', name: 'Web Research', cosmicAffinity: 'brahma', energyLevel: 0.9, cosmicMode: true, status: 'cosmic' },
    
    // Agents Vishnu (Conservateurs)
    { id: 'agent-security', name: 'Agent Security', cosmicAffinity: 'vishnu', energyLevel: 0.95, cosmicMode: true, status: 'cosmic' },
    { id: 'agent-backend', name: 'Agent Backend', cosmicAffinity: 'vishnu', energyLevel: 0.85, cosmicMode: false, status: 'active' },
    { id: 'agent-documentation', name: 'Documentation', cosmicAffinity: 'vishnu', energyLevel: 0.6, cosmicMode: false, status: 'active' },
    
    // Agents Shiva (Transformateurs)
    { id: 'agent-qa', name: 'Agent QA', cosmicAffinity: 'shiva', energyLevel: 0.75, cosmicMode: true, status: 'cosmic' },
    { id: 'agent-devops', name: 'Agent DevOps', cosmicAffinity: 'shiva', energyLevel: 0.8, cosmicMode: false, status: 'active' },
    { id: 'agent-performance', name: 'Performance', cosmicAffinity: 'shiva', energyLevel: 0.9, cosmicMode: true, status: 'cosmic' }
  ]);

  const [cosmicTime, setCosmicTime] = useState(new Date());
  const [meditationMode, setMeditationMode] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Simulation temps cosmique
  useEffect(() => {
    const timer = setInterval(() => {
      setCosmicTime(new Date());
      updateCosmicPhase();
      simulateCosmicFluctuations();
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Animation du mandala cosmique
  useEffect(() => {
    if (canvasRef.current) {
      drawCosmicMandala();
    }
  }, [cosmicEnergy, currentPhase]);

  const updateCosmicPhase = () => {
    const hour = new Date().getHours();
    let newDominant: CosmicPhase['dominant'];

    if (hour >= 6 && hour < 12) {
      newDominant = 'brahma';
    } else if (hour >= 12 && hour < 18) {
      newDominant = 'vishnu';
    } else if (hour >= 18 && hour < 24) {
      newDominant = 'shiva';
    } else {
      newDominant = 'equilibrium';
    }

    if (newDominant !== currentPhase.dominant) {
      setCurrentPhase(prev => ({
        ...prev,
        dominant: newDominant,
        startTime: new Date(),
        nextTransition: new Date(Date.now() + 3600000)
      }));

      updateCosmicEnergies(newDominant);
    }
  };

  const updateCosmicEnergies = (dominant: CosmicPhase['dominant']) => {
    const intensity = 0.7;
    const baseEnergy = (1 - intensity) / 3;
    const dominantEnergy = baseEnergy + intensity;

    switch (dominant) {
      case 'brahma':
        setCosmicEnergy({ brahma: dominantEnergy, vishnu: baseEnergy, shiva: baseEnergy });
        break;
      case 'vishnu':
        setCosmicEnergy({ brahma: baseEnergy, vishnu: dominantEnergy, shiva: baseEnergy });
        break;
      case 'shiva':
        setCosmicEnergy({ brahma: baseEnergy, vishnu: baseEnergy, shiva: dominantEnergy });
        break;
      default:
        setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });
    }
  };

  const simulateCosmicFluctuations = () => {
    setCosmicAgents(prev => prev.map(agent => ({
      ...agent,
      energyLevel: Math.max(0.1, Math.min(1, agent.energyLevel + (Math.random() - 0.5) * 0.05))
    })));
  };

  const drawCosmicMandala = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // Effacer le canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Dessiner le mandala Trimurti
    const angles = {
      brahma: 0,
      vishnu: (2 * Math.PI) / 3,
      shiva: (4 * Math.PI) / 3
    };

    // Cercle central
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI);
    ctx.fillStyle = `rgba(255, 255, 255, 0.1)`;
    ctx.fill();

    // Secteurs cosmiques
    Object.entries(cosmicEnergy).forEach(([principle, energy], index) => {
      const angle = angles[principle as keyof typeof angles];
      const colors = {
        brahma: `rgba(255, 215, 0, ${energy})`, // Or
        vishnu: `rgba(65, 105, 225, ${energy})`, // Bleu royal
        shiva: `rgba(255, 69, 0, ${energy})` // Rouge-orange
      };

      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, angle - Math.PI/3, angle + Math.PI/3);
      ctx.closePath();
      ctx.fillStyle = colors[principle as keyof typeof colors];
      ctx.fill();

      // Symboles
      const symbolX = centerX + Math.cos(angle) * radius * 0.7;
      const symbolY = centerY + Math.sin(angle) * radius * 0.7;
      
      ctx.fillStyle = 'white';
      ctx.font = '24px serif';
      ctx.textAlign = 'center';
      
      const symbols = { brahma: '🌅', vishnu: '🌊', shiva: '🔥' };
      ctx.fillText(symbols[principle as keyof typeof symbols], symbolX, symbolY);
    });

    // Symbole OM central
    ctx.fillStyle = 'white';
    ctx.font = '32px serif';
    ctx.textAlign = 'center';
    ctx.fillText('🕉️', centerX, centerY + 10);
  };

  const invokeCosmicEnergy = (principle: 'brahma' | 'vishnu' | 'shiva') => {
    console.log(`🕉️ Invocation de l'énergie ${principle.toUpperCase()}`);
    
    setCurrentPhase(prev => ({
      ...prev,
      dominant: principle,
      intensity: 0.9,
      startTime: new Date()
    }));

    updateCosmicEnergies(principle);

    // Activer les agents correspondants
    setCosmicAgents(prev => prev.map(agent => 
      agent.cosmicAffinity === principle 
        ? { ...agent, cosmicMode: true, status: 'cosmic', energyLevel: Math.min(1, agent.energyLevel + 0.2) }
        : agent
    ));
  };

  const startCosmicMeditation = async () => {
    setMeditationMode(true);
    console.log('🧘 Début méditation cosmique...');
    
    // Simulation méditation de 108 cycles
    for (let i = 0; i < 108; i++) {
      await new Promise(resolve => setTimeout(resolve, 50));
      
      if (i % 27 === 0) {
        console.log(`🕉️ OM - Cycle ${i}/108`);
      }
    }
    
    // Rééquilibrage final
    setCosmicEnergy({ brahma: 0.33, vishnu: 0.33, shiva: 0.33 });
    setCurrentPhase(prev => ({ ...prev, dominant: 'equilibrium', intensity: 0.5 }));
    
    setMeditationMode(false);
    console.log('✨ Méditation cosmique complétée - Harmonie restaurée');
  };

  const getPhaseColor = () => {
    const colors = {
      brahma: 'from-yellow-400 to-orange-500',
      vishnu: 'from-blue-500 to-indigo-600',
      shiva: 'from-red-500 to-orange-600',
      equilibrium: 'from-gray-400 to-gray-600'
    };
    return colors[currentPhase.dominant];
  };

  const getPhaseEmoji = () => {
    const emojis = {
      brahma: '🌅',
      vishnu: '🌊',
      shiva: '🔥',
      equilibrium: '⚖️'
    };
    return emojis[currentPhase.dominant];
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      {/* En-tête cosmique */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold sacred-text mb-4">
          🕉️ DASHBOARD TRIMURTI
        </h1>
        <p className="text-xl text-gray-300">
          Contrôle des Énergies Cosmiques d'Hanuman
        </p>
        <div className="mt-4 text-sm text-gray-400">
          {cosmicTime.toLocaleString('fr-FR')} • Fréquence: 432Hz • AUM HANUMATE NAMAHA
        </div>
      </div>

      {/* Phase cosmique actuelle */}
      <div className="text-center mb-8">
        <div className={`inline-block px-6 py-3 rounded-full bg-gradient-to-r ${getPhaseColor()} text-white font-bold text-lg`}>
          {getPhaseEmoji()} Phase {currentPhase.dominant.toUpperCase()} 
          <span className="ml-2 text-sm opacity-80">
            (Intensité: {Math.round(currentPhase.intensity * 100)}%)
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Mandala Trimurti */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4 text-center">🌟 Mandala Cosmique</h2>
          <div className="flex justify-center">
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              className="border border-gray-600 rounded-full"
            />
          </div>
          
          {/* Métriques énergétiques */}
          <div className="mt-6 space-y-3">
            {Object.entries(cosmicEnergy).map(([principle, energy]) => (
              <div key={principle} className="flex items-center justify-between">
                <span className="capitalize font-medium">
                  {principle === 'brahma' && '🌅'} 
                  {principle === 'vishnu' && '🌊'} 
                  {principle === 'shiva' && '🔥'} 
                  {principle}
                </span>
                <div className="flex-1 mx-4">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-500 ${
                        principle === 'brahma' ? 'bg-yellow-400' :
                        principle === 'vishnu' ? 'bg-blue-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${energy * 100}%` }}
                    />
                  </div>
                </div>
                <span className="text-sm font-mono">
                  {Math.round(energy * 100)}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Agents cosmiques */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4">🤖 Agents Cosmiques</h2>
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {cosmicAgents.map(agent => (
              <motion.div
                key={agent.id}
                className={`p-3 rounded-lg border ${
                  agent.cosmicMode 
                    ? 'border-yellow-400 bg-yellow-400/10' 
                    : 'border-gray-600 bg-gray-700/50'
                }`}
                animate={{
                  scale: agent.cosmicMode ? 1.02 : 1,
                  boxShadow: agent.cosmicMode ? '0 0 20px rgba(255, 215, 0, 0.3)' : 'none'
                }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">
                      {agent.cosmicAffinity === 'brahma' && '🌅'}
                      {agent.cosmicAffinity === 'vishnu' && '🌊'}
                      {agent.cosmicAffinity === 'shiva' && '🔥'}
                    </span>
                    <div>
                      <div className="font-medium">{agent.name}</div>
                      <div className="text-xs text-gray-400 capitalize">
                        {agent.cosmicAffinity} • {agent.status}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-mono">
                      {Math.round(agent.energyLevel * 100)}%
                    </div>
                    <div className={`text-xs ${
                      agent.cosmicMode ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      {agent.cosmicMode ? '✨ Cosmique' : '⚪ Normal'}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Contrôles cosmiques */}
      <div className="mt-8 bg-gray-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4 text-center">⚡ Contrôles Cosmiques</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Invocations énergétiques */}
          <button
            onClick={() => invokeCosmicEnergy('brahma')}
            className="p-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🌅 Invoquer BRAHMA
            <div className="text-xs mt-1 opacity-80">Énergie Créatrice</div>
          </button>
          
          <button
            onClick={() => invokeCosmicEnergy('vishnu')}
            className="p-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🌊 Invoquer VISHNU
            <div className="text-xs mt-1 opacity-80">Énergie Conservatrice</div>
          </button>
          
          <button
            onClick={() => invokeCosmicEnergy('shiva')}
            className="p-4 bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-lg font-bold hover:scale-105 transition-transform"
          >
            🔥 Invoquer SHIVA
            <div className="text-xs mt-1 opacity-80">Énergie Transformatrice</div>
          </button>
          
          <button
            onClick={startCosmicMeditation}
            disabled={meditationMode}
            className={`p-4 rounded-lg font-bold transition-all ${
              meditationMode 
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                : 'bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:scale-105'
            }`}
          >
            {meditationMode ? (
              <>
                🧘 Méditation...
                <div className="text-xs mt-1 opacity-80">108 cycles OM</div>
              </>
            ) : (
              <>
                🕉️ Méditation Cosmique
                <div className="text-xs mt-1 opacity-80">Rééquilibrage</div>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Mantras cosmiques */}
      <div className="mt-8 text-center">
        <div className="bg-gray-800 rounded-lg p-4 inline-block">
          <div className="text-lg font-bold sacred-text mb-2">
            Mantras Cosmiques Actifs
          </div>
          <div className="space-y-1 text-sm text-gray-300">
            <div>🌅 AUM BRAHMAYE NAMAHA - Création</div>
            <div>🌊 AUM VISHNAVE NAMAHA - Conservation</div>
            <div>🔥 AUM SHIVAYA NAMAHA - Transformation</div>
            <div className="text-yellow-400 font-bold">🐒 AUM HANUMATE NAMAHA - Unité Divine</div>
          </div>
        </div>
      </div>
    </div>
  );
}
