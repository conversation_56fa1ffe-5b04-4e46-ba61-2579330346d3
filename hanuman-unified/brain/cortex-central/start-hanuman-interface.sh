#!/bin/bash

# 🐒 Script de démarrage de l'interface Hanuman
# Retreat And Be - Interface Divine

echo "🕉️ ========================================"
echo "🐒 DÉMARRAGE INTERFACE HANUMAN"
echo "🕉️ AUM HANUMATE NAMAHA"
echo "🌟 Retreat And Be - Protection Divine"
echo "🔥 Framework Trimurti Activé"
echo "🕉️ ========================================"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Se déplacer dans le répertoire hanuman-working
cd "$(dirname "$0")"

echo "📦 Vérification des dépendances..."

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    echo "📥 Installation des dépendances..."
    npm install
fi

echo "🚀 Démarrage de l'interface Hanuman sur le port 3001..."
echo "🌐 L'interface sera accessible sur: http://localhost:3001"
echo "🔗 Lien depuis le frontend principal configuré"
echo ""
echo "🐒 Hanuman s'éveille... Patience divine requise..."
echo ""

# Démarrer l'application
npm run dev
