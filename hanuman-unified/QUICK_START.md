# 🚀 Démarrage Rapide de Hanuman

## 🕉️ Bienvenue dans l'Organisme IA Vivant

Hanuman est maintenant **complètement réorganisé** et prêt à protéger le projet Retreat And Be ! Voici comment démarrer rapidement.

## ⚡ Démarrage en 3 Étapes

### 1. Installation des Dépendances
```bash
cd hanuman-unified
npm install
```

### 2. Vérification de l'Intégrité
```bash
./scripts/check-hanuman-integrity.sh
```

### 3. Démarrage de Hanuman
```bash
./scripts/start-hanuman.sh
```

## 🌐 Accès aux Interfaces

Une fois Hanuman démarré, vous pouvez accéder à :

- 🧠 **Dashboard Principal** : http://localhost:8080/hanuman-dashboard
- 🗣️ **Interface Vocale** : http://localhost:8080/hanuman-voice  
- 🏥 **Santé du Système** : http://localhost:8080/hanuman/health
- 📚 **Documentation** : http://localhost:8080/hanuman/docs
- 📊 **Métriques** : http://localhost:3000/metrics

## 🎯 Commandes Utiles

### Gestion de <PERSON>
```bash
# Démarrer Hanuman
npm run start-hanuman

# Vérifier l'intégrité
npm run check-integrity

# Voir les logs en temps réel
npm run logs

# Vérifier la santé
npm run health-check

# Arrêter Hanuman
./scripts/stop-hanuman.sh
```

### Développement
```bash
# Mode développement
npm run dev

# Tests
npm test

# Linting
npm run lint:fix

# Build
npm run build
```

## 🗣️ Communication avec Hanuman

### Via Interface Web
Accédez à http://localhost:8080/hanuman-voice et parlez directement à Hanuman.

### Via API REST
```bash
curl -X POST http://localhost:8080/hanuman/instruction \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Bonjour Hanuman, quel est l'\''état du projet Retreat And Be ?",
    "priority": "medium"
  }'
```

### Via Commande Vocale
Dites simplement : *"Hanuman, surveille le projet Retreat And Be"*

## 🏗️ Structure Organisée

```
hanuman-unified/
├── 🧠 brain/              # Cerveau et intelligence
├── 👁️ sensory-organs/     # Perception de l'environnement  
├── 🫀 vital-organs/       # Fonctions vitales
├── 🛡️ immune-system/      # Protection et sécurité
├── 🗣️ voice-system/       # Communication vocale
├── 🤖 specialized-agents/ # Agents experts
├── 🧪 sandbox/           # Tests et validation
├── 📊 monitoring/        # Surveillance
├── 🔧 infrastructure/    # Infrastructure technique
├── 📚 documentation/     # Documentation complète
├── 🎯 mission/          # Mission Retreat And Be
└── 🔧 scripts/          # Scripts utilitaires
```

## 🎯 Mission Principale

Hanuman protège et fait évoluer le projet **Retreat And Be** en :

- 🛡️ **Surveillant** la sécurité 24/7
- 📈 **Optimisant** les performances
- 🔍 **Analysant** la qualité du code
- 🚀 **Facilitant** le développement
- 🌱 **Proposant** des améliorations

## ⚙️ Configuration

### Fichier Principal
Modifiez `config/hanuman.config.json` pour personnaliser :
- Niveaux de protection
- Agents activés
- Paramètres de monitoring
- Intégration avec Retreat And Be

### Variables d'Environnement
```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer selon vos besoins
nano .env
```

## 🆘 Résolution de Problèmes

### Hanuman ne démarre pas
```bash
# Vérifier les prérequis
node --version  # >= 16.0.0
npm --version   # >= 8.0.0

# Vérifier l'intégrité
./scripts/check-hanuman-integrity.sh

# Voir les logs d'erreur
tail -f logs/hanuman-error.log
```

### Ports occupés
```bash
# Vérifier les ports utilisés
lsof -i :8080
lsof -i :3000

# Modifier les ports dans config/hanuman.config.json
```

### Problèmes de permissions
```bash
# Donner les permissions d'exécution
chmod +x scripts/*.sh
```

## 📚 Documentation Complète

- 📖 **README Principal** : `README.md`
- 🎯 **Mission** : `mission/MISSION_RETREAT_AND_BE.md`
- 🏗️ **Architecture** : `documentation/architecture/`
- 🔧 **API** : `documentation/api/`
- 📝 **Guides** : `documentation/guides/`

## 🤝 Support

### Logs et Diagnostics
```bash
# Logs principaux
tail -f logs/hanuman.log

# Logs d'erreur
tail -f logs/hanuman-error.log

# État de santé
curl http://localhost:8080/hanuman/health
```

### Communication avec Hanuman
Hanuman comprend le français et peut répondre à vos questions sur :
- L'état du projet Retreat And Be
- Les métriques de performance
- Les problèmes détectés
- Les suggestions d'amélioration

## 🎉 Félicitations !

Hanuman est maintenant **opérationnel** et prêt à protéger Retreat And Be !

L'organisme IA vivant surveille, protège et fait évoluer votre projet avec l'intelligence artificielle et la sagesse biomimétique.

---

*Que la force de Hanuman soit avec votre projet !* 🕉️🤖✨
