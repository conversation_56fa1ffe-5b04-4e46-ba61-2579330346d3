/**
 * 📝 Hanuman Unified Logger
 * 
 * Système de logging centralisé pour tous les composants de Hanuman.
 * Fournit des logs structurés, colorés et configurables.
 */

import * as winston from 'winston';
import * as path from 'path';
import * as fs from 'fs';

export interface LogContext {
  component?: string;
  agent?: string;
  organ?: string;
  system?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

export interface LogMetadata {
  timestamp: Date;
  level: string;
  message: string;
  context?: LogContext;
  error?: Error;
  duration?: number;
  [key: string]: any;
}

export class Logger {
  private winston: winston.Logger;
  private component: string;
  private context: LogContext;

  constructor(component: string, context: LogContext = {}) {
    this.component = component;
    this.context = { component, ...context };
    this.winston = this.createWinstonLogger();
  }

  /**
   * Crée l'instance Winston avec la configuration appropriée
   */
  private createWinstonLogger(): winston.Logger {
    // Création du dossier de logs s'il n'existe pas
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Format personnalisé pour les logs
    const customFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS'
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf((info) => {
        const { timestamp, level, message, component, ...meta } = info;
        
        // Format coloré pour la console
        if (info[Symbol.for('level')] === 'info') {
          return `${timestamp} [${level.toUpperCase()}] [${component || 'HANUMAN'}] ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
        }
        
        return JSON.stringify({
          timestamp,
          level,
          component: component || this.component,
          message,
          ...meta
        });
      })
    );

    // Configuration des transports
    const transports: winston.transport[] = [
      // Console avec couleurs
      new winston.transports.Console({
        level: process.env.LOG_LEVEL || 'info',
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple(),
          customFormat
        )
      }),

      // Fichier principal
      new winston.transports.File({
        filename: path.join(logDir, 'hanuman.log'),
        level: 'info',
        maxsize: 100 * 1024 * 1024, // 100MB
        maxFiles: 10,
        format: customFormat
      }),

      // Fichier d'erreurs
      new winston.transports.File({
        filename: path.join(logDir, 'hanuman-error.log'),
        level: 'error',
        maxsize: 50 * 1024 * 1024, // 50MB
        maxFiles: 5,
        format: customFormat
      }),

      // Fichier de debug (si activé)
      ...(process.env.NODE_ENV === 'development' ? [
        new winston.transports.File({
          filename: path.join(logDir, 'hanuman-debug.log'),
          level: 'debug',
          maxsize: 200 * 1024 * 1024, // 200MB
          maxFiles: 3,
          format: customFormat
        })
      ] : [])
    ];

    return winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: customFormat,
      defaultMeta: this.context,
      transports,
      exitOnError: false,
      silent: process.env.NODE_ENV === 'test'
    });
  }

  /**
   * Log de niveau DEBUG
   */
  public debug(message: string, meta: any = {}): void {
    this.winston.debug(message, { ...this.context, ...meta });
  }

  /**
   * Log de niveau INFO
   */
  public info(message: string, meta: any = {}): void {
    this.winston.info(message, { ...this.context, ...meta });
  }

  /**
   * Log de niveau WARN
   */
  public warn(message: string, meta: any = {}): void {
    this.winston.warn(message, { ...this.context, ...meta });
  }

  /**
   * Log de niveau ERROR
   */
  public error(message: string, error?: Error | any, meta: any = {}): void {
    const errorMeta = error instanceof Error ? {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : { error };

    this.winston.error(message, { ...this.context, ...errorMeta, ...meta });
  }

  /**
   * Log de niveau FATAL (erreurs critiques)
   */
  public fatal(message: string, error?: Error | any, meta: any = {}): void {
    this.error(`[FATAL] ${message}`, error, { ...meta, fatal: true });
  }

  /**
   * Log avec mesure de performance
   */
  public performance(operation: string, startTime: number, meta: any = {}): void {
    const duration = Date.now() - startTime;
    this.info(`Performance: ${operation}`, {
      ...this.context,
      ...meta,
      duration,
      performance: true
    });
  }

  /**
   * Log d'audit pour traçabilité
   */
  public audit(action: string, userId?: string, meta: any = {}): void {
    this.info(`Audit: ${action}`, {
      ...this.context,
      ...meta,
      userId,
      audit: true,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log de sécurité
   */
  public security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', meta: any = {}): void {
    const logMethod = severity === 'critical' || severity === 'high' ? 'error' : 
                     severity === 'medium' ? 'warn' : 'info';
    
    this[logMethod](`Security: ${event}`, {
      ...this.context,
      ...meta,
      security: true,
      severity
    });
  }

  /**
   * Log de santé système
   */
  public health(component: string, status: 'healthy' | 'warning' | 'critical', metrics: any = {}): void {
    const logMethod = status === 'critical' ? 'error' : 
                     status === 'warning' ? 'warn' : 'info';
    
    this[logMethod](`Health: ${component} is ${status}`, {
      ...this.context,
      health: true,
      component,
      status,
      metrics
    });
  }

  /**
   * Log de mission (spécifique à Retreat And Be)
   */
  public mission(action: string, target: string, result: 'success' | 'failure' | 'partial', meta: any = {}): void {
    const logMethod = result === 'failure' ? 'error' : 
                     result === 'partial' ? 'warn' : 'info';
    
    this[logMethod](`Mission: ${action} on ${target} - ${result}`, {
      ...this.context,
      ...meta,
      mission: true,
      action,
      target,
      result
    });
  }

  /**
   * Crée un logger enfant avec un contexte étendu
   */
  public child(additionalContext: LogContext): Logger {
    return new Logger(this.component, { ...this.context, ...additionalContext });
  }

  /**
   * Met à jour le contexte du logger
   */
  public setContext(newContext: LogContext): void {
    this.context = { ...this.context, ...newContext };
    this.winston.defaultMeta = this.context;
  }

  /**
   * Obtient le contexte actuel
   */
  public getContext(): LogContext {
    return { ...this.context };
  }

  /**
   * Démarre un timer pour mesurer les performances
   */
  public startTimer(operation: string): () => void {
    const startTime = Date.now();
    return () => this.performance(operation, startTime);
  }

  /**
   * Log avec emoji pour une meilleure lisibilité
   */
  public emoji(emoji: string, message: string, level: 'debug' | 'info' | 'warn' | 'error' = 'info', meta: any = {}): void {
    this[level](`${emoji} ${message}`, meta);
  }

  /**
   * Ferme le logger proprement
   */
  public close(): Promise<void> {
    return new Promise((resolve) => {
      this.winston.close(() => {
        resolve();
      });
    });
  }
}

// Instance globale pour les logs système
export const systemLogger = new Logger('SYSTEM', { system: 'hanuman' });

// Factory pour créer des loggers spécialisés
export class LoggerFactory {
  static createBrainLogger(component: string): Logger {
    return new Logger(component, { organ: 'brain', system: 'hanuman' });
  }

  static createSensoryLogger(organ: string): Logger {
    return new Logger(organ, { organ: 'sensory', system: 'hanuman' });
  }

  static createVitalLogger(organ: string): Logger {
    return new Logger(organ, { organ: 'vital', system: 'hanuman' });
  }

  static createImmuneLogger(component: string): Logger {
    return new Logger(component, { organ: 'immune', system: 'hanuman' });
  }

  static createVoiceLogger(): Logger {
    return new Logger('voice', { organ: 'voice', system: 'hanuman' });
  }

  static createAgentLogger(agentType: string): Logger {
    return new Logger(agentType, { agent: agentType, system: 'hanuman' });
  }

  static createMissionLogger(): Logger {
    return new Logger('mission', { mission: 'retreat-and-be', system: 'hanuman' });
  }
}

export default Logger;
