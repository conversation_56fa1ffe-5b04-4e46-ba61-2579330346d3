#!/bin/bash

# 🐒 Script de Démarrage Agent <PERSON><PERSON> LLM
# AUM HANUMATE NAMAHA - Gardien <PERSON> du Projet Retreat & Be

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage avec couleurs
print_header() {
    echo -e "${PURPLE}🕉️ ========================================${NC}"
    echo -e "${PURPLE}🐒 DÉMARRAGE AGENT HANUMAN LLM${NC}"
    echo -e "${PURPLE}🕉️ AUM HANUMATE NAMAHA${NC}"
    echo -e "${PURPLE}🌟 Retreat And Be - G<PERSON>ien <PERSON>${NC}"
    echo -e "${PURPLE}🔥 Architecture Neuronale Distribuée${NC}"
    echo -e "${PURPLE}🕉️ ========================================${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_DIR/.env"
LOG_DIR="$PROJECT_DIR/logs"
PID_FILE="$PROJECT_DIR/hanuman.pid"

# Fonction de vérification des prérequis
check_prerequisites() {
    print_step "Vérification des prérequis..."
    
    # Vérification Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js n'est pas installé"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js version 18+ requis (version actuelle: $(node --version))"
        exit 1
    fi
    
    # Vérification npm
    if ! command -v npm &> /dev/null; then
        print_error "npm n'est pas installé"
        exit 1
    fi
    
    print_success "Prérequis validés"
}

# Fonction de configuration de l'environnement
setup_environment() {
    print_step "Configuration de l'environnement..."
    
    # Création du répertoire de logs
    mkdir -p "$LOG_DIR"
    
    # Vérification du fichier .env
    if [ ! -f "$ENV_FILE" ]; then
        print_warning "Fichier .env non trouvé, création depuis .env.example"
        if [ -f "$PROJECT_DIR/.env.example" ]; then
            cp "$PROJECT_DIR/.env.example" "$ENV_FILE"
            print_info "Veuillez configurer le fichier .env avant de continuer"
            print_info "Éditez: $ENV_FILE"
            exit 1
        else
            print_error "Fichier .env.example non trouvé"
            exit 1
        fi
    fi
    
    print_success "Environnement configuré"
}

# Fonction d'installation des dépendances
install_dependencies() {
    print_step "Installation des dépendances..."
    
    cd "$PROJECT_DIR"
    
    if [ ! -d "node_modules" ]; then
        print_info "Installation des dépendances npm..."
        npm install
    else
        print_info "Vérification des dépendances..."
        npm ci --only=production
    fi
    
    print_success "Dépendances installées"
}

# Fonction de compilation TypeScript
build_project() {
    print_step "Compilation du projet..."
    
    cd "$PROJECT_DIR"
    
    if [ ! -d "dist" ] || [ "$1" = "--force-build" ]; then
        print_info "Compilation TypeScript..."
        npm run build
    else
        print_info "Projet déjà compilé (utilisez --force-build pour recompiler)"
    fi
    
    print_success "Projet compilé"
}

# Fonction de vérification des services
check_services() {
    print_step "Vérification des services dépendants..."
    
    # Liste des services à vérifier
    declare -A services=(
        ["Redis"]="localhost:6379"
        ["PostgreSQL"]="localhost:5432"
        ["Frontend"]="localhost:3000"
        ["Backend"]="localhost:3001"
    )
    
    for service in "${!services[@]}"; do
        local endpoint="${services[$service]}"
        local host=$(echo "$endpoint" | cut -d':' -f1)
        local port=$(echo "$endpoint" | cut -d':' -f2)
        
        if timeout 3 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
            print_success "$service disponible ($endpoint)"
        else
            print_warning "$service non disponible ($endpoint)"
        fi
    done
}

# Fonction de démarrage de Hanuman
start_hanuman() {
    print_step "Démarrage d'Hanuman..."
    
    cd "$PROJECT_DIR"
    
    # Vérification si Hanuman est déjà en cours d'exécution
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            print_warning "Hanuman est déjà en cours d'exécution (PID: $pid)"
            return 0
        else
            print_info "Suppression du fichier PID obsolète"
            rm -f "$PID_FILE"
        fi
    fi
    
    # Démarrage selon l'environnement
    if [ "${NODE_ENV:-development}" = "production" ]; then
        print_info "Démarrage en mode production..."
        nohup npm start > "$LOG_DIR/hanuman.log" 2>&1 &
    else
        print_info "Démarrage en mode développement..."
        nohup npm run dev > "$LOG_DIR/hanuman-dev.log" 2>&1 &
    fi
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # Attente du démarrage
    print_info "Attente du démarrage d'Hanuman..."
    sleep 5
    
    # Vérification du démarrage
    if ps -p "$pid" > /dev/null 2>&1; then
        print_success "Hanuman démarré avec succès (PID: $pid)"
        
        # Test de l'API
        local api_port="${HANUMAN_API_PORT:-5003}"
        if curl -f "http://localhost:$api_port/health" > /dev/null 2>&1; then
            print_success "API Hanuman opérationnelle (Port: $api_port)"
        else
            print_warning "API Hanuman non accessible, vérifiez les logs"
        fi
    else
        print_error "Échec du démarrage d'Hanuman"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# Fonction d'affichage des informations
display_info() {
    print_step "Informations de démarrage..."
    
    local api_port="${HANUMAN_API_PORT:-5003}"
    local dashboard_port="${HANUMAN_DASHBOARD_PORT:-8080}"
    
    echo ""
    echo -e "${CYAN}🔗 ENDPOINTS DISPONIBLES:${NC}"
    echo -e "   📡 API Hanuman: ${GREEN}http://localhost:$api_port${NC}"
    echo -e "   💬 Chat Interface: ${GREEN}http://localhost:$api_port/chat${NC}"
    echo -e "   📊 Health Check: ${GREEN}http://localhost:$api_port/health${NC}"
    echo -e "   📈 Dashboard: ${GREEN}http://localhost:$dashboard_port${NC}"
    
    echo ""
    echo -e "${CYAN}🧠 AGENTS SURVEILLÉS:${NC}"
    echo -e "   🏠 Frontend React (Port 3000)"
    echo -e "   ⚙️ Backend NestJS (Port 3001)"
    echo -e "   🤖 Agent-RB (Port 5000)"
    echo -e "   🧠 Agent-IA (Port 5002)"
    echo -e "   🎯 SuperAgent (Port 5001)"
    
    echo ""
    echo -e "${CYAN}🎯 CAPACITÉS ACTIVES:${NC}"
    echo -e "   💬 Interface conversationnelle LLM"
    echo -e "   🎯 Orchestration multi-agents"
    echo -e "   👁️ Monitoring système temps réel"
    echo -e "   🛡️ Protection et auto-réparation"
    echo -e "   🧠 Prédiction et optimisation"
    
    echo ""
    echo -e "${CYAN}📋 COMMANDES UTILES:${NC}"
    echo -e "   📊 Statut: ${YELLOW}curl http://localhost:$api_port/health${NC}"
    echo -e "   📜 Logs: ${YELLOW}tail -f $LOG_DIR/hanuman.log${NC}"
    echo -e "   🛑 Arrêt: ${YELLOW}$SCRIPT_DIR/stop-hanuman.sh${NC}"
    echo -e "   🔄 Redémarrage: ${YELLOW}$SCRIPT_DIR/restart-hanuman.sh${NC}"
    
    echo ""
    echo -e "${PURPLE}🕉️ Hanuman veille sur Retreat & Be ✨${NC}"
    echo -e "${PURPLE}🐒 Gardien spirituel en service${NC}"
}

# Fonction d'intégration avec l'interface chat
integrate_chat_interface() {
    print_step "Intégration avec l'interface chat..."
    
    local chat_interface_dir="$PROJECT_DIR/../../hanuman-working"
    
    if [ -d "$chat_interface_dir" ]; then
        # Mise à jour de l'API endpoint dans l'interface chat
        local api_endpoint="http://localhost:${HANUMAN_API_PORT:-5003}/api/chat"
        
        print_info "Configuration de l'endpoint API: $api_endpoint"
        
        # Création d'un fichier de configuration pour l'interface
        cat > "$chat_interface_dir/hanuman-config.json" << EOF
{
  "hanumanAPI": {
    "endpoint": "$api_endpoint",
    "healthCheck": "http://localhost:${HANUMAN_API_PORT:-5003}/health",
    "websocket": "ws://localhost:${HANUMAN_API_PORT:-5003}/ws"
  },
  "features": {
    "realTimeMonitoring": true,
    "agentCoordination": true,
    "systemAlerts": true,
    "predictiveAnalysis": true
  },
  "ui": {
    "theme": "spiritual",
    "animations": true,
    "agentVisualization": true
  }
}
EOF
        
        print_success "Interface chat configurée"
    else
        print_warning "Interface chat non trouvée dans $chat_interface_dir"
    fi
}

# Fonction principale
main() {
    print_header
    
    # Traitement des arguments
    local force_build=false
    local skip_services=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-build)
                force_build=true
                shift
                ;;
            --skip-services)
                skip_services=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --force-build    Force la recompilation"
                echo "  --skip-services  Ignore la vérification des services"
                echo "  --help, -h       Affiche cette aide"
                exit 0
                ;;
            *)
                print_error "Option inconnue: $1"
                exit 1
                ;;
        esac
    done
    
    # Exécution des étapes
    check_prerequisites
    setup_environment
    install_dependencies
    
    if [ "$force_build" = true ]; then
        build_project --force-build
    else
        build_project
    fi
    
    if [ "$skip_services" = false ]; then
        check_services
    fi
    
    start_hanuman
    integrate_chat_interface
    display_info
    
    echo ""
    print_success "Hanuman est opérationnel ! 🐒✨"
    echo -e "${PURPLE}🕉️ AUM HANUMATE NAMAHA${NC}"
}

# Gestion des signaux
trap 'print_error "Interruption détectée"; exit 1' INT TERM

# Exécution du script principal
main "$@"
