/**
 * 🎤 Types pour l'Organe Vocal d'Hanuman
 * Intégration avec l'architecture neuronale distribuée
 */

// ===== TYPES DE BASE ORGANE VOCAL =====

export interface VoiceOrganConfig {
  organId: string;
  organName: string;
  llmProvider: 'openai' | 'anthropic' | 'local';
  llmModel: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  languages: string[];
  orchestratorConnection: OrchestratorConnection;
  neuralProcessing: NeuralProcessingConfig;
}

export interface OrchestratorConnection {
  host: string;
  port: number;
  protocol: 'ws' | 'http';
  reconnectAttempts: number;
  heartbeatInterval: number;
}

export interface NeuralProcessingConfig {
  signalProcessingInterval: number;
  maxSignalsPerCycle: number;
  priorityWeights: PriorityWeights;
  synapticStrength: number;
}

export interface PriorityWeights {
  critical: number;
  high: number;
  medium: number;
  low: number;
}

// ===== TYPES ORGANE ET ÉTAT =====

export interface OrganState {
  id: string;
  name: string;
  type: 'vocal' | 'sensory' | 'cognitive' | 'motor' | 'emotional';
  status: 'inactive' | 'active' | 'processing' | 'error';
  connectedAgents: string[];
  lastActivity: Date;
  metrics: OrganMetrics;
  neuralConnections: string[];
}

export interface OrganMetrics {
  activityLevel: number; // 0-100
  responseTime: number; // ms
  successRate: number; // 0-100
  dataProcessed: number;
  conversationsHandled: number;
  averageConfidence: number;
  emotionalResonance: number;
}

// ===== TYPES SIGNAUX NEURAUX =====

export interface NeuralSignal {
  id: string;
  source: string; // ID de l'organe source
  target: string; // ID de l'organe cible
  type: NeuralSignalType;
  data: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  processed: boolean;
  synapticPath?: string[];
}

export type NeuralSignalType = 
  | 'USER_MESSAGE'
  | 'VOICE_RESPONSE'
  | 'EMOTIONAL_STATE'
  | 'COGNITIVE_PROCESS'
  | 'SENSORY_INPUT'
  | 'MOTOR_ACTION'
  | 'SYSTEM_STATUS'
  | 'AGENT_COORDINATION'
  | 'MEMORY_ACCESS'
  | 'LEARNING_UPDATE';

// ===== TYPES CONNEXIONS SYNAPTIQUES =====

export interface SynapticConnection {
  id: string;
  fromOrgan: string;
  toOrgan: string;
  strength: number; // 0-1
  type: 'excitatory' | 'inhibitory' | 'modulatory';
  lastActivation: Date;
  activationCount: number;
  plasticity: number; // Capacité d'adaptation
}

// ===== TYPES MESSAGES ET COMMUNICATION =====

export interface AgentMessage {
  id: string;
  agentId: string;
  type: string;
  content: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  targetOrgan?: string;
}

export interface UserMessage {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
  context?: UserContext;
  metadata?: Record<string, any>;
}

export interface UserContext {
  sessionId: string;
  preferences: UserPreferences;
  emotionalState?: EmotionalState;
  conversationHistory: ConversationEntry[];
  location?: GeoLocation;
}

export interface UserPreferences {
  language: string;
  communicationStyle: 'formal' | 'casual' | 'spiritual' | 'technical';
  responseLength: 'brief' | 'normal' | 'detailed';
  emotionalTone: 'neutral' | 'empathetic' | 'encouraging' | 'wise';
  topics: string[];
}

export interface EmotionalState {
  primary: EmotionType;
  intensity: number; // 0-1
  secondary?: EmotionType[];
  stability: number; // 0-1
  resonance: number; // 0-1 (avec Hanuman)
}

export type EmotionType = 
  | 'joy' | 'peace' | 'love' | 'gratitude' | 'wonder'
  | 'sadness' | 'fear' | 'anger' | 'confusion' | 'longing'
  | 'curiosity' | 'determination' | 'hope' | 'trust' | 'compassion';

export interface ConversationEntry {
  timestamp: Date;
  userMessage: string;
  hanumanResponse: string;
  emotionalResonance: number;
  satisfaction?: number;
  topics: string[];
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  country: string;
  city: string;
  timezone: string;
  culturalContext?: string;
}

// ===== TYPES RÉPONSE VOCALE =====

export interface VoiceResponse {
  id: string;
  content: string;
  type: VoiceResponseType;
  confidence: number;
  emotionalTone: EmotionType;
  agentsInvolved: string[];
  organsActivated: string[];
  processingTime: number;
  metadata: VoiceResponseMetadata;
  timestamp: Date;
}

export type VoiceResponseType = 
  | 'direct-answer'
  | 'emotional-support'
  | 'spiritual-guidance'
  | 'technical-explanation'
  | 'creative-expression'
  | 'question-clarification'
  | 'agent-coordination'
  | 'system-status';

export interface VoiceResponseMetadata {
  tokensUsed: number;
  modelUsed: string;
  reasoningPath: string[];
  emotionalAnalysis: EmotionalAnalysis;
  spiritualReferences: string[];
  technicalConcepts: string[];
  sources: string[];
  confidence: ConfidenceBreakdown;
}

export interface EmotionalAnalysis {
  userEmotion: EmotionType;
  responseEmotion: EmotionType;
  empathyLevel: number;
  resonanceAchieved: number;
  healingPotential: number;
}

export interface ConfidenceBreakdown {
  overall: number;
  factual: number;
  emotional: number;
  spiritual: number;
  technical: number;
}

// ===== TYPES ANALYSE ET TRAITEMENT =====

export interface MessageAnalysis {
  intent: MessageIntent;
  entities: ExtractedEntity[];
  sentiment: SentimentAnalysis;
  emotionalState: EmotionalState;
  spiritualContext: SpiritualContext;
  complexity: number;
  requiresOrganCoordination: boolean;
  targetOrgans: string[];
  confidence: number;
  language: string;
}

export interface MessageIntent {
  primary: string;
  secondary?: string[];
  confidence: number;
  category: IntentCategory;
  spiritualDimension?: SpiritualDimension;
}

export type IntentCategory = 
  | 'information' | 'emotional-support' | 'spiritual-guidance'
  | 'technical-help' | 'creative-request' | 'social-interaction'
  | 'system-query' | 'agent-coordination' | 'emergency';

export type SpiritualDimension = 
  | 'devotion' | 'wisdom' | 'compassion' | 'service'
  | 'protection' | 'healing' | 'transformation' | 'unity';

export interface ExtractedEntity {
  type: EntityType;
  value: string;
  confidence: number;
  start: number;
  end: number;
  spiritualSignificance?: number;
}

export type EntityType = 
  | 'person' | 'location' | 'date' | 'emotion' | 'concept'
  | 'spiritual-practice' | 'deity' | 'mantra' | 'chakra'
  | 'technical-term' | 'agent-name' | 'system-component';

export interface SentimentAnalysis {
  polarity: number; // -1 to 1
  subjectivity: number; // 0 to 1
  emotion: EmotionType;
  intensity: number; // 0 to 1
  confidence: number;
  spiritualAlignment: number; // 0 to 1
}

export interface SpiritualContext {
  tradition: string[];
  concepts: string[];
  practices: string[];
  deities: string[];
  mantras: string[];
  chakras: string[];
  significance: number; // 0 to 1
}

// ===== TYPES ÉVÉNEMENTS =====

export interface VoiceOrganEvent {
  id: string;
  type: VoiceEventType;
  source: string;
  data: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  organState?: OrganState;
}

export type VoiceEventType = 
  | 'organ-activated' | 'organ-deactivated'
  | 'neural-signal-received' | 'neural-signal-sent'
  | 'user-message-received' | 'voice-response-generated'
  | 'emotional-state-changed' | 'spiritual-insight-gained'
  | 'agent-coordination-requested' | 'system-status-updated'
  | 'learning-update' | 'error-occurred';

// ===== TYPES COORDINATION =====

export interface OrganCoordinationRequest {
  id: string;
  requestingOrgan: string;
  targetOrgans: string[];
  purpose: string;
  data: any;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timeout: number;
  expectedResponse: string;
}

export interface OrganCoordinationResponse {
  requestId: string;
  respondingOrgan: string;
  status: 'success' | 'error' | 'timeout' | 'partial';
  data?: any;
  error?: string;
  processingTime: number;
  timestamp: Date;
}

// ===== EXPORTS =====

export default {
  VoiceOrganConfig,
  OrganState,
  NeuralSignal,
  AgentMessage,
  UserMessage,
  VoiceResponse,
  MessageAnalysis,
  VoiceOrganEvent
};
