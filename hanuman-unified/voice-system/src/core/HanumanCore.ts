/**
 * 🎤 Hanuman Voice Organ - Organe Vocal de l'Être I<PERSON>
 * S'intègre dans l'architecture neuronale distribuée existante
 */

import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger';
import { HanumanVoiceLLM } from './voice/HanumanVoiceLLM';
import { VoiceOrganConnector } from './connectors/VoiceOrganConnector';
import {
  VoiceOrganConfig,
  OrganState,
  NeuralSignal,
  AgentMessage,
  VoiceResponse
} from '../types/VoiceOrganTypes';

export class HanumanCore extends EventEmitter {
  private logger: Logger;
  private voice: <PERSON><PERSON>Voice;
  private brain: <PERSON>umanBrain;
  private immune: HanumanImmune;
  private communicator: AgentCommunicator;
  private monitor: SystemMonitor;
  private config: HanumanConfig;
  private isActive: boolean = false;
  private systemContext: SystemContext;

  constructor(config: HanumanConfig) {
    super();
    this.config = config;
    this.logger = new Logger('HanumanCore');

    // Initialisation des composants principaux
    this.initializeComponents();

    // Configuration des événements
    this.setupEventHandlers();

    this.logger.info('🐒 Hanuman Core initialisé - AUM HANUMATE NAMAHA');
  }

  /**
   * Initialise tous les composants de Hanuman
   */
  private initializeComponents(): void {
    try {
      // Voix - Interface de communication
      this.voice = new HanumanVoice(this.config.voice);

      // Cerveau - Orchestration et décisions
      this.brain = new HanumanBrain(this.config.brain);

      // Système immunitaire - Protection et sécurité
      this.immune = new HanumanImmune(this.config.immune);

      // Communicateur - Interface avec autres agents
      this.communicator = new AgentCommunicator(this.config.communication);

      // Moniteur - Surveillance système
      this.monitor = new SystemMonitor(this.config.monitoring);

      // Contexte système initial
      this.systemContext = {
        timestamp: new Date(),
        systemHealth: 'unknown',
        activeAgents: [],
        currentLoad: 0,
        alerts: []
      };

      this.logger.info('✅ Tous les composants Hanuman initialisés');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation des composants:', error);
      throw error;
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements de la voix (chat utilisateur)
    this.voice.on('userMessage', this.handleUserMessage.bind(this));
    this.voice.on('voiceError', this.handleVoiceError.bind(this));

    // Événements du cerveau (décisions)
    this.brain.on('decisionMade', this.handleDecision.bind(this));
    this.brain.on('optimizationNeeded', this.handleOptimization.bind(this));

    // Événements du système immunitaire (sécurité)
    this.immune.on('threatDetected', this.handleThreat.bind(this));
    this.immune.on('healingCompleted', this.handleHealing.bind(this));

    // Événements du communicateur (agents)
    this.communicator.on('agentResponse', this.handleAgentResponse.bind(this));
    this.communicator.on('agentOffline', this.handleAgentOffline.bind(this));

    // Événements du moniteur (surveillance)
    this.monitor.on('systemAlert', this.handleSystemAlert.bind(this));
    this.monitor.on('performanceUpdate', this.handlePerformanceUpdate.bind(this));

    this.logger.info('🔗 Gestionnaires d\'événements configurés');
  }

  /**
   * Démarre Hanuman et tous ses composants
   */
  public async start(): Promise<void> {
    try {
      this.logger.info('🚀 Démarrage de Hanuman...');

      // Démarrage séquentiel des composants
      await this.monitor.start();
      await this.communicator.start();
      await this.immune.start();
      await this.brain.start();
      await this.voice.start();

      this.isActive = true;

      // Mise à jour du contexte système
      await this.updateSystemContext();

      // Émission de l'événement de démarrage
      this.emit('hanumanStarted', {
        timestamp: new Date(),
        status: 'active',
        components: ['voice', 'brain', 'immune', 'communicator', 'monitor']
      });

      this.logger.info('✅ Hanuman démarré avec succès - Gardien actif');
    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage de Hanuman:', error);
      throw error;
    }
  }

  /**
   * Arrête Hanuman proprement
   */
  public async stop(): Promise<void> {
    try {
      this.logger.info('🛑 Arrêt de Hanuman...');

      this.isActive = false;

      // Arrêt séquentiel des composants
      await this.voice.stop();
      await this.brain.stop();
      await this.immune.stop();
      await this.communicator.stop();
      await this.monitor.stop();

      this.emit('hanumanStopped', {
        timestamp: new Date(),
        status: 'inactive'
      });

      this.logger.info('✅ Hanuman arrêté proprement');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'arrêt de Hanuman:', error);
      throw error;
    }
  }

  /**
   * Traite un message utilisateur
   */
  private async handleUserMessage(event: HanumanEvent): Promise<void> {
    try {
      const { message, userId, context } = event.data;

      this.logger.info(`💬 Message utilisateur reçu: ${message.substring(0, 50)}...`);

      // Analyse du message par le cerveau
      const analysis = await this.brain.analyzeUserRequest(message, context);

      // Génération de la réponse par la voix
      const response = await this.voice.generateResponse(analysis, this.systemContext);

      // Si des actions sont nécessaires, coordination des agents
      if (analysis.requiresAgentAction) {
        await this.coordinateAgents(analysis.agentRequests);
      }

      // Émission de la réponse
      this.emit('responseGenerated', {
        userId,
        response,
        analysis,
        timestamp: new Date()
      });

    } catch (error) {
      this.logger.error('❌ Erreur lors du traitement du message utilisateur:', error);
      this.emit('responseError', { error: error.message });
    }
  }

  /**
   * Coordonne les agents pour exécuter une tâche
   */
  private async coordinateAgents(requests: AgentRequest[]): Promise<void> {
    try {
      this.logger.info(`🎯 Coordination de ${requests.length} agents`);

      // Orchestration par le cerveau
      const workflow = await this.brain.createWorkflow(requests);

      // Exécution via le communicateur
      const results = await this.communicator.executeWorkflow(workflow);

      // Analyse des résultats
      await this.brain.analyzeWorkflowResults(results);

      this.emit('agentCoordinationCompleted', {
        workflow,
        results,
        timestamp: new Date()
      });

    } catch (error) {
      this.logger.error('❌ Erreur lors de la coordination des agents:', error);
    }
  }

  /**
   * Met à jour le contexte système
   */
  private async updateSystemContext(): Promise<void> {
    try {
      const systemStatus = await this.monitor.getSystemStatus();
      const activeAgents = await this.communicator.getActiveAgents();
      const securityStatus = await this.immune.getSecurityStatus();

      this.systemContext = {
        timestamp: new Date(),
        systemHealth: systemStatus.health,
        activeAgents,
        currentLoad: systemStatus.load,
        alerts: systemStatus.alerts,
        securityLevel: securityStatus.level,
        performance: systemStatus.performance
      };

      this.emit('contextUpdated', this.systemContext);
    } catch (error) {
      this.logger.error('❌ Erreur lors de la mise à jour du contexte:', error);
    }
  }

  /**
   * Gestionnaires d'événements spécialisés
   */
  private handleVoiceError(event: HanumanEvent): void {
    this.logger.error('🎤 Erreur de voix:', event.data);
    this.emit('componentError', { component: 'voice', error: event.data });
  }

  private handleDecision(event: HanumanEvent): void {
    this.logger.info('🧠 Décision prise:', event.data.decision);
    this.emit('decisionMade', event.data);
  }

  private handleOptimization(event: HanumanEvent): void {
    this.logger.info('⚡ Optimisation nécessaire:', event.data.area);
    this.emit('optimizationTriggered', event.data);
  }

  private handleThreat(event: HanumanEvent): void {
    this.logger.warn('🛡️ Menace détectée:', event.data.threat);
    this.emit('securityAlert', event.data);
  }

  private handleHealing(event: HanumanEvent): void {
    this.logger.info('🔧 Auto-réparation terminée:', event.data.healing);
    this.emit('systemHealed', event.data);
  }

  private handleAgentResponse(event: HanumanEvent): void {
    this.logger.debug('📡 Réponse d\'agent reçue:', event.data.agentId);
    this.emit('agentResponseReceived', event.data);
  }

  private handleAgentOffline(event: HanumanEvent): void {
    this.logger.warn('📴 Agent hors ligne:', event.data.agentId);
    this.emit('agentOffline', event.data);
  }

  private handleSystemAlert(event: HanumanEvent): void {
    this.logger.warn('⚠️ Alerte système:', event.data.alert);
    this.emit('systemAlert', event.data);
  }

  private handlePerformanceUpdate(event: HanumanEvent): void {
    this.logger.debug('📊 Mise à jour performance:', event.data.metrics);
    this.updateSystemContext();
  }

  /**
   * Getters publics
   */
  public getSystemContext(): SystemContext {
    return this.systemContext;
  }

  public isHanumanActive(): boolean {
    return this.isActive;
  }

  public getComponentStatus(): Record<string, boolean> {
    return {
      voice: this.voice.isActive(),
      brain: this.brain.isActive(),
      immune: this.immune.isActive(),
      communicator: this.communicator.isActive(),
      monitor: this.monitor.isActive()
    };
  }
}

export default HanumanCore;
