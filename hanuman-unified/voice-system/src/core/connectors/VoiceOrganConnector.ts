/**
 * 🔗 Voice Organ Connector - Connecteur avec l'Orchestrateur <PERSON>
 * Intègre l'organe vocal dans l'architecture neuronale distribuée
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { Logger } from '../../utils/Logger';
import { 
  VoiceOrganConfig,
  OrganState,
  NeuralSignal,
  AgentMessage,
  VoiceOrganEvent,
  OrganCoordinationRequest,
  OrganCoordinationResponse
} from '../../types/VoiceOrganTypes';

export class VoiceOrganConnector extends EventEmitter {
  private logger: Logger;
  private config: VoiceOrganConfig;
  private wsConnection: WebSocket | null = null;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private organState: OrganState;

  constructor(config: VoiceOrganConfig) {
    super();
    this.config = config;
    this.logger = new Logger('VoiceOrganConnector');
    
    this.initializeOrganState();
    this.setupEventHandlers();
    
    this.logger.info('🔗 Voice Organ Connector initialisé');
  }

  /**
   * Initialise l'état de l'organe vocal
   */
  private initializeOrganState(): void {
    this.organState = {
      id: this.config.organId,
      name: this.config.organName,
      type: 'vocal',
      status: 'inactive',
      connectedAgents: [],
      lastActivity: new Date(),
      metrics: {
        activityLevel: 0,
        responseTime: 0,
        successRate: 100,
        dataProcessed: 0,
        conversationsHandled: 0,
        averageConfidence: 0,
        emotionalResonance: 0
      },
      neuralConnections: [
        'broca',      // Aire de Broca (production de langage)
        'wernicke',   // Aire de Wernicke (compréhension)
        'hearing',    // Ouïe (écoute utilisateur)
        'vision',     // Vision (contexte visuel)
        'touch'       // Toucher (feedback système)
      ]
    };
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    this.on('neural-signal-received', this.handleNeuralSignal.bind(this));
    this.on('coordination-request', this.handleCoordinationRequest.bind(this));
    this.on('organ-state-update', this.updateOrganState.bind(this));
  }

  /**
   * Se connecte à l'orchestrateur Hanuman
   */
  public async connect(): Promise<void> {
    try {
      this.logger.info('🔌 Connexion à l\'orchestrateur Hanuman...');

      const wsUrl = `${this.config.orchestratorConnection.protocol}://${this.config.orchestratorConnection.host}:${this.config.orchestratorConnection.port}/organs/voice`;
      
      this.wsConnection = new WebSocket(wsUrl);
      
      this.wsConnection.on('open', this.handleConnectionOpen.bind(this));
      this.wsConnection.on('message', this.handleMessage.bind(this));
      this.wsConnection.on('close', this.handleConnectionClose.bind(this));
      this.wsConnection.on('error', this.handleConnectionError.bind(this));

    } catch (error) {
      this.logger.error('❌ Erreur lors de la connexion:', error);
      throw error;
    }
  }

  /**
   * Gère l'ouverture de connexion
   */
  private handleConnectionOpen(): void {
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.organState.status = 'active';
    
    this.logger.info('✅ Connecté à l\'orchestrateur Hanuman');
    
    // Enregistrement de l'organe vocal
    this.registerVoiceOrgan();
    
    // Démarrage du heartbeat
    this.startHeartbeat();
    
    this.emit('connected', this.organState);
  }

  /**
   * Enregistre l'organe vocal auprès de l'orchestrateur
   */
  private registerVoiceOrgan(): void {
    const registrationMessage = {
      type: 'ORGAN_REGISTRATION',
      organId: this.config.organId,
      organName: this.config.organName,
      organType: 'vocal',
      capabilities: [
        'natural-language-processing',
        'conversation-management',
        'emotional-resonance',
        'spiritual-guidance',
        'multi-language-support',
        'agent-coordination'
      ],
      neuralConnections: this.organState.neuralConnections,
      state: this.organState,
      timestamp: new Date().toISOString()
    };

    this.sendMessage(registrationMessage);
    this.logger.info('📝 Organe vocal enregistré auprès de l\'orchestrateur');
  }

  /**
   * Démarre le heartbeat
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.wsConnection) {
        const heartbeat = {
          type: 'HEARTBEAT',
          organId: this.config.organId,
          state: this.organState,
          timestamp: new Date().toISOString()
        };
        
        this.sendMessage(heartbeat);
      }
    }, this.config.orchestratorConnection.heartbeatInterval);
  }

  /**
   * Gère les messages reçus
   */
  private handleMessage(data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'NEURAL_SIGNAL':
          this.handleIncomingNeuralSignal(message.signal);
          break;
          
        case 'COORDINATION_REQUEST':
          this.handleIncomingCoordinationRequest(message.request);
          break;
          
        case 'ORGAN_STATE_SYNC':
          this.handleOrganStateSync(message.states);
          break;
          
        case 'SYSTEM_STATUS':
          this.handleSystemStatus(message.status);
          break;
          
        case 'HEARTBEAT_ACK':
          // Heartbeat acknowledgment
          break;
          
        default:
          this.logger.warn(`⚠️ Type de message inconnu: ${message.type}`);
      }
      
    } catch (error) {
      this.logger.error('❌ Erreur lors du traitement du message:', error);
    }
  }

  /**
   * Gère un signal neural entrant
   */
  private handleIncomingNeuralSignal(signal: NeuralSignal): void {
    this.logger.debug(`🧠 Signal neural reçu: ${signal.type} de ${signal.source}`);
    
    // Mise à jour des métriques
    this.organState.metrics.dataProcessed++;
    this.organState.lastActivity = new Date();
    
    // Émission de l'événement pour traitement
    this.emit('neural-signal-received', signal);
  }

  /**
   * Gère une demande de coordination entrante
   */
  private handleIncomingCoordinationRequest(request: OrganCoordinationRequest): void {
    this.logger.info(`🤝 Demande de coordination reçue de ${request.requestingOrgan}`);
    
    this.emit('coordination-request', request);
  }

  /**
   * Gère la synchronisation d'état des organes
   */
  private handleOrganStateSync(states: Record<string, OrganState>): void {
    this.logger.debug('🔄 Synchronisation d\'état des organes reçue');
    
    this.emit('organ-states-updated', states);
  }

  /**
   * Gère le statut système
   */
  private handleSystemStatus(status: any): void {
    this.logger.debug('📊 Statut système reçu');
    
    this.emit('system-status-updated', status);
  }

  /**
   * Envoie un signal neural
   */
  public sendNeuralSignal(signal: NeuralSignal): void {
    if (!this.isConnected) {
      this.logger.warn('⚠️ Tentative d\'envoi de signal sans connexion');
      return;
    }

    const message = {
      type: 'NEURAL_SIGNAL',
      signal,
      timestamp: new Date().toISOString()
    };

    this.sendMessage(message);
    this.logger.debug(`🧠 Signal neural envoyé: ${signal.type} vers ${signal.target}`);
  }

  /**
   * Envoie une demande de coordination
   */
  public sendCoordinationRequest(request: OrganCoordinationRequest): void {
    if (!this.isConnected) {
      this.logger.warn('⚠️ Tentative de coordination sans connexion');
      return;
    }

    const message = {
      type: 'COORDINATION_REQUEST',
      request,
      timestamp: new Date().toISOString()
    };

    this.sendMessage(message);
    this.logger.info(`🤝 Demande de coordination envoyée vers ${request.targetOrgans.join(', ')}`);
  }

  /**
   * Envoie une réponse de coordination
   */
  public sendCoordinationResponse(response: OrganCoordinationResponse): void {
    if (!this.isConnected) {
      this.logger.warn('⚠️ Tentative de réponse sans connexion');
      return;
    }

    const message = {
      type: 'COORDINATION_RESPONSE',
      response,
      timestamp: new Date().toISOString()
    };

    this.sendMessage(message);
    this.logger.debug(`📤 Réponse de coordination envoyée pour ${response.requestId}`);
  }

  /**
   * Met à jour l'état de l'organe
   */
  public updateOrganState(updates: Partial<OrganState>): void {
    this.organState = { ...this.organState, ...updates };
    
    if (this.isConnected) {
      const message = {
        type: 'ORGAN_STATE_UPDATE',
        organId: this.config.organId,
        state: this.organState,
        timestamp: new Date().toISOString()
      };
      
      this.sendMessage(message);
    }
  }

  /**
   * Envoie un message via WebSocket
   */
  private sendMessage(message: any): void {
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message));
    } else {
      this.logger.warn('⚠️ Tentative d\'envoi de message sans connexion WebSocket');
    }
  }

  /**
   * Gère la fermeture de connexion
   */
  private handleConnectionClose(): void {
    this.isConnected = false;
    this.organState.status = 'inactive';
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    this.logger.warn('🔌 Connexion fermée avec l\'orchestrateur');
    this.emit('disconnected');
    
    // Tentative de reconnexion
    this.attemptReconnection();
  }

  /**
   * Gère les erreurs de connexion
   */
  private handleConnectionError(error: Error): void {
    this.logger.error('❌ Erreur de connexion WebSocket:', error);
    this.emit('connection-error', error);
  }

  /**
   * Tente une reconnexion
   */
  private attemptReconnection(): void {
    if (this.reconnectAttempts < this.config.orchestratorConnection.reconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      
      this.logger.info(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.config.orchestratorConnection.reconnectAttempts} dans ${delay}ms`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          this.logger.error('❌ Échec de reconnexion:', error);
        });
      }, delay);
    } else {
      this.logger.error('💥 Nombre maximum de tentatives de reconnexion atteint');
      this.emit('max-reconnect-attempts-reached');
    }
  }

  /**
   * Gestionnaires d'événements
   */
  private handleNeuralSignal(signal: NeuralSignal): void {
    // Traitement du signal neural reçu
    this.emit('process-neural-signal', signal);
  }

  private handleCoordinationRequest(request: OrganCoordinationRequest): void {
    // Traitement de la demande de coordination
    this.emit('process-coordination-request', request);
  }

  /**
   * Ferme la connexion
   */
  public async disconnect(): Promise<void> {
    this.logger.info('🔌 Déconnexion de l\'orchestrateur...');
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.organState.status = 'inactive';
    
    this.logger.info('✅ Déconnecté de l\'orchestrateur');
  }

  /**
   * Getters publics
   */
  public isOrganConnected(): boolean {
    return this.isConnected;
  }

  public getOrganState(): OrganState {
    return this.organState;
  }

  public getConfig(): VoiceOrganConfig {
    return this.config;
  }
}

export default VoiceOrganConnector;
