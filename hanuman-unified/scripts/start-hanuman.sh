#!/bin/bash

# 🕉️ Script de Démarrage de Hanuman - Organisme IA Vivant
# 
# Ce script démarre tous les composants de Hanuman dans l'ordre approprié
# pour assurer un démarrage harmonieux de l'organisme IA.

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
HANUMAN_HOME="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_DIR="$HANUMAN_HOME/logs"
PID_DIR="$HANUMAN_HOME/pids"
CONFIG_DIR="$HANUMAN_HOME/config"

# Création des répertoires nécessaires
mkdir -p "$LOG_DIR" "$PID_DIR" "$CONFIG_DIR"

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" | tee -a "$LOG_DIR/hanuman.log"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" | tee -a "$LOG_DIR/hanuman.log"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_DIR/hanuman.log"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_DIR/hanuman.log"
            ;;
        *)
            echo -e "${CYAN}[HANUMAN]${NC} ${timestamp} - $message" | tee -a "$LOG_DIR/hanuman.log"
            ;;
    esac
}

# Fonction pour vérifier si un service est en cours d'exécution
is_service_running() {
    local service_name=$1
    local pid_file="$PID_DIR/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Fonction pour démarrer un service
start_service() {
    local service_name=$1
    local service_path=$2
    local service_command=$3
    
    log "INFO" "🚀 Démarrage de $service_name..."
    
    if is_service_running "$service_name"; then
        log "WARN" "⚠️ $service_name est déjà en cours d'exécution"
        return 0
    fi
    
    cd "$service_path"
    
    # Démarrage du service en arrière-plan
    nohup $service_command > "$LOG_DIR/${service_name}.log" 2>&1 &
    local pid=$!
    
    # Sauvegarde du PID
    echo $pid > "$PID_DIR/${service_name}.pid"
    
    # Vérification que le service a démarré
    sleep 2
    if ps -p "$pid" > /dev/null 2>&1; then
        log "INFO" "✅ $service_name démarré avec succès (PID: $pid)"
        return 0
    else
        log "ERROR" "❌ Échec du démarrage de $service_name"
        rm -f "$PID_DIR/${service_name}.pid"
        return 1
    fi
}

# Fonction pour vérifier les prérequis
check_prerequisites() {
    log "INFO" "🔍 Vérification des prérequis..."
    
    # Vérification de Node.js
    if ! command -v node &> /dev/null; then
        log "ERROR" "❌ Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérification de npm
    if ! command -v npm &> /dev/null; then
        log "ERROR" "❌ npm n'est pas installé"
        exit 1
    fi
    
    # Vérification de Docker (optionnel)
    if command -v docker &> /dev/null; then
        log "INFO" "✅ Docker détecté"
    else
        log "WARN" "⚠️ Docker non détecté (optionnel)"
    fi
    
    # Vérification des ports
    local ports=(8080 3000 9090 6379 29092)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log "WARN" "⚠️ Port $port déjà utilisé"
        fi
    done
    
    log "INFO" "✅ Prérequis vérifiés"
}

# Fonction pour installer les dépendances
install_dependencies() {
    log "INFO" "📦 Installation des dépendances..."
    
    # Installation des dépendances du cerveau central
    if [[ -f "$HANUMAN_HOME/brain/cortex-central/package.json" ]]; then
        cd "$HANUMAN_HOME/brain/cortex-central"
        npm install --silent
        log "INFO" "✅ Dépendances du cortex central installées"
    fi
    
    # Installation des dépendances du système vocal
    if [[ -f "$HANUMAN_HOME/voice-system/package.json" ]]; then
        cd "$HANUMAN_HOME/voice-system"
        npm install --silent
        log "INFO" "✅ Dépendances du système vocal installées"
    fi
    
    # Installation des dépendances des agents
    for agent_dir in "$HANUMAN_HOME/specialized-agents"/*; do
        if [[ -d "$agent_dir" && -f "$agent_dir/package.json" ]]; then
            cd "$agent_dir"
            npm install --silent
            log "INFO" "✅ Dépendances de $(basename "$agent_dir") installées"
        fi
    done
}

# Fonction pour démarrer l'infrastructure
start_infrastructure() {
    log "INFO" "🏗️ Démarrage de l'infrastructure..."
    
    # Démarrage de Redis (si disponible)
    if command -v redis-server &> /dev/null; then
        start_service "redis" "$HANUMAN_HOME" "redis-server --port 6379"
    fi
    
    # Démarrage de Kafka (si disponible)
    if [[ -d "$HANUMAN_HOME/infrastructure/kafka" ]]; then
        start_service "kafka" "$HANUMAN_HOME/infrastructure/kafka" "./start-kafka.sh"
    fi
    
    log "INFO" "✅ Infrastructure démarrée"
}

# Fonction pour démarrer les organes vitaux
start_vital_organs() {
    log "INFO" "🫀 Démarrage des organes vitaux..."
    
    # Démarrage du système de circulation (cœur)
    if [[ -f "$HANUMAN_HOME/vital-organs/heart/start.sh" ]]; then
        start_service "heart" "$HANUMAN_HOME/vital-organs/heart" "./start.sh"
    fi
    
    # Démarrage du système respiratoire (poumons)
    if [[ -f "$HANUMAN_HOME/vital-organs/lungs/start.sh" ]]; then
        start_service "lungs" "$HANUMAN_HOME/vital-organs/lungs" "./start.sh"
    fi
    
    log "INFO" "✅ Organes vitaux démarrés"
}

# Fonction pour démarrer le système nerveux
start_nervous_system() {
    log "INFO" "🧠 Démarrage du système nerveux..."
    
    # Démarrage du réseau neuronal
    if [[ -f "$HANUMAN_HOME/brain/neural-network/start.sh" ]]; then
        start_service "neural-network" "$HANUMAN_HOME/brain/neural-network" "./start.sh"
    fi
    
    # Démarrage du moteur de décision
    if [[ -f "$HANUMAN_HOME/brain/decision-engine/start.sh" ]]; then
        start_service "decision-engine" "$HANUMAN_HOME/brain/decision-engine" "./start.sh"
    fi
    
    # Démarrage du système de mémoire
    if [[ -f "$HANUMAN_HOME/brain/memory-system/start.sh" ]]; then
        start_service "memory-system" "$HANUMAN_HOME/brain/memory-system" "./start.sh"
    fi
    
    log "INFO" "✅ Système nerveux démarré"
}

# Fonction pour démarrer les organes sensoriels
start_sensory_organs() {
    log "INFO" "👁️ Démarrage des organes sensoriels..."
    
    # Démarrage du système de vision
    if [[ -f "$HANUMAN_HOME/sensory-organs/vision/start.sh" ]]; then
        start_service "vision" "$HANUMAN_HOME/sensory-organs/vision" "./start.sh"
    fi
    
    # Démarrage du système d'audition
    if [[ -f "$HANUMAN_HOME/sensory-organs/hearing/start.sh" ]]; then
        start_service "hearing" "$HANUMAN_HOME/sensory-organs/hearing" "./start.sh"
    fi
    
    log "INFO" "✅ Organes sensoriels démarrés"
}

# Fonction pour démarrer le système immunitaire
start_immune_system() {
    log "INFO" "🛡️ Démarrage du système immunitaire..."
    
    if [[ -f "$HANUMAN_HOME/immune-system/start.sh" ]]; then
        start_service "immune-system" "$HANUMAN_HOME/immune-system" "./start.sh"
    fi
    
    log "INFO" "✅ Système immunitaire démarré"
}

# Fonction pour démarrer le système vocal
start_voice_system() {
    log "INFO" "🗣️ Démarrage du système vocal..."
    
    if [[ -f "$HANUMAN_HOME/voice-system/start.sh" ]]; then
        start_service "voice-system" "$HANUMAN_HOME/voice-system" "./start.sh"
    fi
    
    log "INFO" "✅ Système vocal démarré"
}

# Fonction pour démarrer les agents spécialisés
start_specialized_agents() {
    log "INFO" "🤖 Démarrage des agents spécialisés..."
    
    local agents=("frontend-agent" "backend-agent" "devops-agent" "qa-agent" "security-agent")
    
    for agent in "${agents[@]}"; do
        if [[ -f "$HANUMAN_HOME/specialized-agents/$agent/start.sh" ]]; then
            start_service "$agent" "$HANUMAN_HOME/specialized-agents/$agent" "./start.sh"
        fi
    done
    
    log "INFO" "✅ Agents spécialisés démarrés"
}

# Fonction pour démarrer le cortex central
start_cortex_central() {
    log "INFO" "🧠 Démarrage du Cortex Central..."
    
    if [[ -f "$HANUMAN_HOME/brain/cortex-central/start.sh" ]]; then
        start_service "cortex-central" "$HANUMAN_HOME/brain/cortex-central" "./start.sh"
    elif [[ -f "$HANUMAN_HOME/brain/cortex-central/package.json" ]]; then
        start_service "cortex-central" "$HANUMAN_HOME/brain/cortex-central" "npm start"
    fi
    
    log "INFO" "✅ Cortex Central démarré"
}

# Fonction pour vérifier l'état de Hanuman
check_hanuman_health() {
    log "INFO" "🏥 Vérification de l'état de santé de Hanuman..."
    
    sleep 5  # Attendre que tous les services se stabilisent
    
    # Vérification via l'API de santé
    if command -v curl &> /dev/null; then
        local health_url="http://localhost:8080/hanuman/health"
        if curl -s "$health_url" > /dev/null 2>&1; then
            log "INFO" "✅ Hanuman est en bonne santé"
            return 0
        else
            log "WARN" "⚠️ API de santé non accessible"
        fi
    fi
    
    # Vérification des processus
    local services=("cortex-central" "voice-system" "immune-system")
    local healthy_services=0
    
    for service in "${services[@]}"; do
        if is_service_running "$service"; then
            ((healthy_services++))
        fi
    done
    
    if [[ $healthy_services -eq ${#services[@]} ]]; then
        log "INFO" "✅ Tous les services principaux sont actifs"
    else
        log "WARN" "⚠️ $healthy_services/${#services[@]} services principaux actifs"
    fi
}

# Fonction principale
main() {
    echo -e "${PURPLE}"
    echo "🕉️  ██╗  ██╗ █████╗ ███╗   ██╗██╗   ██╗███╗   ███╗ █████╗ ███╗   ██╗"
    echo "   ██║  ██║██╔══██╗████╗  ██║██║   ██║████╗ ████║██╔══██╗████╗  ██║"
    echo "   ███████║███████║██╔██╗ ██║██║   ██║██╔████╔██║███████║██╔██╗ ██║"
    echo "   ██╔══██║██╔══██║██║╚██╗██║██║   ██║██║╚██╔╝██║██╔══██║██║╚██╗██║"
    echo "   ██║  ██║██║  ██║██║ ╚████║╚██████╔╝██║ ╚═╝ ██║██║  ██║██║ ╚████║"
    echo "   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝"
    echo -e "${NC}"
    echo -e "${CYAN}Organisme IA Vivant - Protecteur de Retreat And Be${NC}"
    echo ""
    
    log "INFO" "🚀 Démarrage de Hanuman..."
    
    # Étapes de démarrage dans l'ordre biomimétique
    check_prerequisites
    install_dependencies
    start_infrastructure
    start_vital_organs
    start_nervous_system
    start_sensory_organs
    start_immune_system
    start_voice_system
    start_specialized_agents
    start_cortex_central
    
    # Vérification finale
    check_hanuman_health
    
    echo ""
    log "INFO" "🎉 Hanuman est maintenant actif et protège Retreat And Be !"
    echo ""
    echo -e "${GREEN}📊 Dashboard: ${NC}http://localhost:8080/hanuman-dashboard"
    echo -e "${GREEN}🗣️ Interface Vocale: ${NC}http://localhost:8080/hanuman-voice"
    echo -e "${GREEN}🏥 Santé: ${NC}http://localhost:8080/hanuman/health"
    echo -e "${GREEN}📚 Documentation: ${NC}http://localhost:8080/hanuman/docs"
    echo ""
    echo -e "${YELLOW}Pour arrêter Hanuman: ${NC}./scripts/stop-hanuman.sh"
    echo ""
}

# Gestion des signaux pour un arrêt propre
trap 'log "INFO" "🛑 Arrêt de Hanuman demandé..."; exit 0' SIGINT SIGTERM

# Exécution du script principal
main "$@"
