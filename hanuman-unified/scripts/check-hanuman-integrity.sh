#!/bin/bash

# 🔍 Script de Vérification d'Intégrité <PERSON>
# 
# Ce script vérifie que tous les composants nécessaires de Hanuman
# sont présents et correctement configurés.

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
HANUMAN_HOME="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ERRORS=0
WARNINGS=0

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        "INFO")
            echo -e "${GREEN}[✓]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[⚠]${NC} $message"
            ((WARNINGS++))
            ;;
        "ERROR")
            echo -e "${RED}[✗]${NC} $message"
            ((ERRORS++))
            ;;
        "DEBUG")
            echo -e "${BLUE}[i]${NC} $message"
            ;;
        *)
            echo -e "${CYAN}[H]${NC} $message"
            ;;
    esac
}

# Fonction pour vérifier l'existence d'un fichier
check_file() {
    local file_path="$1"
    local description="$2"
    local required="${3:-true}"
    
    if [[ -f "$HANUMAN_HOME/$file_path" ]]; then
        log "INFO" "$description trouvé"
        return 0
    else
        if [[ "$required" == "true" ]]; then
            log "ERROR" "$description manquant: $file_path"
        else
            log "WARN" "$description optionnel manquant: $file_path"
        fi
        return 1
    fi
}

# Fonction pour vérifier l'existence d'un dossier
check_directory() {
    local dir_path="$1"
    local description="$2"
    local required="${3:-true}"
    
    if [[ -d "$HANUMAN_HOME/$dir_path" ]]; then
        log "INFO" "$description trouvé"
        return 0
    else
        if [[ "$required" == "true" ]]; then
            log "ERROR" "$description manquant: $dir_path"
        else
            log "WARN" "$description optionnel manquant: $dir_path"
        fi
        return 1
    fi
}

# Fonction pour vérifier les permissions d'un fichier
check_permissions() {
    local file_path="$1"
    local expected_perms="$2"
    local description="$3"
    
    if [[ -f "$HANUMAN_HOME/$file_path" ]]; then
        local actual_perms=$(stat -c "%a" "$HANUMAN_HOME/$file_path" 2>/dev/null || stat -f "%A" "$HANUMAN_HOME/$file_path" 2>/dev/null)
        if [[ "$actual_perms" == "$expected_perms" ]]; then
            log "INFO" "Permissions correctes pour $description"
        else
            log "WARN" "Permissions incorrectes pour $description: $actual_perms (attendu: $expected_perms)"
        fi
    fi
}

# Vérification de la structure principale
check_main_structure() {
    log "DEBUG" "Vérification de la structure principale..."
    
    check_file "README.md" "README principal"
    check_file "config/hanuman.config.json" "Configuration principale"
    check_file "mission/MISSION_RETREAT_AND_BE.md" "Documentation de mission"
    
    # Vérification des dossiers principaux
    check_directory "brain" "Dossier cerveau"
    check_directory "sensory-organs" "Dossier organes sensoriels"
    check_directory "vital-organs" "Dossier organes vitaux"
    check_directory "immune-system" "Dossier système immunitaire"
    check_directory "voice-system" "Dossier système vocal"
    check_directory "specialized-agents" "Dossier agents spécialisés"
    check_directory "sandbox" "Dossier sandbox"
    check_directory "monitoring" "Dossier monitoring"
    check_directory "infrastructure" "Dossier infrastructure"
    check_directory "documentation" "Dossier documentation"
    check_directory "mission" "Dossier mission"
    check_directory "scripts" "Dossier scripts"
}

# Vérification du cerveau (brain)
check_brain_components() {
    log "DEBUG" "Vérification des composants du cerveau..."
    
    check_directory "brain/cortex-central" "Cortex Central"
    check_directory "brain/neural-network" "Réseau Neuronal"
    check_directory "brain/decision-engine" "Moteur de Décision"
    check_directory "brain/memory-system" "Système de Mémoire"
    
    check_file "brain/cortex-central/HanumanCore.ts" "Core de Hanuman"
}

# Vérification des organes sensoriels
check_sensory_organs() {
    log "DEBUG" "Vérification des organes sensoriels..."
    
    check_directory "sensory-organs/vision" "Système de Vision"
    check_directory "sensory-organs/hearing" "Système d'Audition"
    check_directory "sensory-organs/touch" "Système Tactile"
    check_directory "sensory-organs/smell" "Système Olfactif"
    check_directory "sensory-organs/taste" "Système Gustatif"
}

# Vérification des organes vitaux
check_vital_organs() {
    log "DEBUG" "Vérification des organes vitaux..."
    
    check_directory "vital-organs/heart" "Cœur (Circulation)"
    check_directory "vital-organs/lungs" "Poumons (Respiration)"
    check_directory "vital-organs/liver" "Foie (Filtrage)"
    check_directory "vital-organs/kidneys" "Reins (Élimination)"
}

# Vérification du système immunitaire
check_immune_system() {
    log "DEBUG" "Vérification du système immunitaire..."
    
    check_directory "immune-system/security-agents" "Agents de Sécurité"
    check_directory "immune-system/auto-healing" "Auto-Guérison"
    check_directory "immune-system/intrusion-detection" "Détection d'Intrusion"
    check_directory "immune-system/quarantine" "Système de Quarantaine"
    
    check_file "immune-system/ImmuneSystem.ts" "Système Immunitaire Principal"
}

# Vérification du système vocal
check_voice_system() {
    log "DEBUG" "Vérification du système vocal..."
    
    check_directory "voice-system/synthesis" "Synthèse Vocale" "false"
    check_directory "voice-system/recognition" "Reconnaissance Vocale" "false"
    check_directory "voice-system/communication" "Communication Vocale" "false"
    check_directory "voice-system/multilingual" "Support Multilingue" "false"
    
    check_file "voice-system/VoiceSystem.ts" "Système Vocal Principal"
}

# Vérification des agents spécialisés
check_specialized_agents() {
    log "DEBUG" "Vérification des agents spécialisés..."
    
    check_directory "specialized-agents/frontend-agent" "Agent Frontend"
    check_directory "specialized-agents/backend-agent" "Agent Backend"
    check_directory "specialized-agents/devops-agent" "Agent DevOps"
    check_directory "specialized-agents/qa-agent" "Agent QA"
    check_directory "specialized-agents/security-agent" "Agent Security"
}

# Vérification du sandbox
check_sandbox() {
    log "DEBUG" "Vérification du sandbox..."
    
    check_directory "sandbox/test-environment" "Environnement de Test"
    check_directory "sandbox/validation" "Système de Validation"
    check_directory "sandbox/deployment-staging" "Staging de Déploiement"
}

# Vérification du monitoring
check_monitoring() {
    log "DEBUG" "Vérification du monitoring..."
    
    check_directory "monitoring/health-dashboard" "Dashboard de Santé"
    check_directory "monitoring/performance-metrics" "Métriques de Performance"
    check_directory "monitoring/alerting" "Système d'Alertes"
}

# Vérification de l'infrastructure
check_infrastructure() {
    log "DEBUG" "Vérification de l'infrastructure..."
    
    check_directory "infrastructure/docker" "Configuration Docker"
    check_directory "infrastructure/kubernetes" "Configuration Kubernetes" "false"
    check_directory "infrastructure/terraform" "Configuration Terraform" "false"
    check_directory "infrastructure/scripts" "Scripts d'Infrastructure" "false"
}

# Vérification des scripts
check_scripts() {
    log "DEBUG" "Vérification des scripts..."
    
    check_file "scripts/start-hanuman.sh" "Script de Démarrage"
    check_file "scripts/check-hanuman-integrity.sh" "Script de Vérification"
    
    # Vérification des permissions d'exécution
    check_permissions "scripts/start-hanuman.sh" "755" "Script de Démarrage"
    check_permissions "scripts/check-hanuman-integrity.sh" "755" "Script de Vérification"
}

# Vérification de la mission
check_mission() {
    log "DEBUG" "Vérification de la mission..."
    
    check_directory "mission/project-protection" "Protection du Projet"
    check_directory "mission/evolution-tracking" "Suivi d'Évolution"
    check_directory "mission/growth-strategies" "Stratégies de Croissance"
    check_directory "mission/retreat-integration" "Intégration Retreat And Be"
}

# Vérification de la configuration
check_configuration() {
    log "DEBUG" "Vérification de la configuration..."
    
    if [[ -f "$HANUMAN_HOME/config/hanuman.config.json" ]]; then
        # Vérification de la syntaxe JSON
        if command -v jq &> /dev/null; then
            if jq empty "$HANUMAN_HOME/config/hanuman.config.json" 2>/dev/null; then
                log "INFO" "Configuration JSON valide"
            else
                log "ERROR" "Configuration JSON invalide"
            fi
        else
            log "WARN" "jq non disponible, impossible de valider le JSON"
        fi
    fi
}

# Vérification des dépendances système
check_system_dependencies() {
    log "DEBUG" "Vérification des dépendances système..."
    
    # Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        log "INFO" "Node.js détecté: $node_version"
    else
        log "ERROR" "Node.js non installé"
    fi
    
    # npm
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version)
        log "INFO" "npm détecté: $npm_version"
    else
        log "ERROR" "npm non installé"
    fi
    
    # Docker (optionnel)
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version)
        log "INFO" "Docker détecté: $docker_version"
    else
        log "WARN" "Docker non installé (optionnel)"
    fi
    
    # Git
    if command -v git &> /dev/null; then
        local git_version=$(git --version)
        log "INFO" "Git détecté: $git_version"
    else
        log "WARN" "Git non installé (recommandé)"
    fi
}

# Vérification de l'intégration avec Retreat And Be
check_retreat_integration() {
    log "DEBUG" "Vérification de l'intégration avec Retreat And Be..."
    
    local project_path="../Projet-RB2"
    if [[ -d "$project_path" ]]; then
        log "INFO" "Projet Retreat And Be détecté"
        
        # Vérification des composants principaux
        if [[ -d "$project_path/Backend-NestJS" ]]; then
            log "INFO" "Backend NestJS détecté"
        else
            log "WARN" "Backend NestJS non trouvé"
        fi
        
        if [[ -d "$project_path/Agent IA" ]]; then
            log "INFO" "Agent IA détecté"
        else
            log "WARN" "Agent IA non trouvé"
        fi
        
    else
        log "ERROR" "Projet Retreat And Be non trouvé: $project_path"
    fi
    
    # Vérification du frontend
    local frontend_path="../Front-Audrey-V1-Main-main"
    if [[ -d "$frontend_path" ]]; then
        log "INFO" "Frontend Audrey détecté"
    else
        log "WARN" "Frontend Audrey non trouvé: $frontend_path"
    fi
}

# Génération du rapport de santé
generate_health_report() {
    local report_file="$HANUMAN_HOME/logs/health-report-$(date +%Y%m%d-%H%M%S).txt"
    mkdir -p "$(dirname "$report_file")"
    
    {
        echo "🕉️ RAPPORT DE SANTÉ DE HANUMAN"
        echo "=============================="
        echo "Date: $(date)"
        echo "Hanuman Home: $HANUMAN_HOME"
        echo ""
        echo "RÉSUMÉ:"
        echo "-------"
        echo "Erreurs: $ERRORS"
        echo "Avertissements: $WARNINGS"
        echo ""
        if [[ $ERRORS -eq 0 ]]; then
            echo "✅ Hanuman est en bonne santé !"
        else
            echo "❌ Hanuman nécessite une attention !"
        fi
        echo ""
        echo "Généré par: check-hanuman-integrity.sh"
    } > "$report_file"
    
    log "INFO" "Rapport de santé généré: $report_file"
}

# Fonction principale
main() {
    echo -e "${PURPLE}"
    echo "🔍 VÉRIFICATION D'INTÉGRITÉ DE HANUMAN"
    echo "======================================"
    echo -e "${NC}"
    
    log "DEBUG" "Démarrage de la vérification d'intégrité..."
    log "DEBUG" "Hanuman Home: $HANUMAN_HOME"
    echo ""
    
    # Exécution de toutes les vérifications
    check_main_structure
    echo ""
    check_brain_components
    echo ""
    check_sensory_organs
    echo ""
    check_vital_organs
    echo ""
    check_immune_system
    echo ""
    check_voice_system
    echo ""
    check_specialized_agents
    echo ""
    check_sandbox
    echo ""
    check_monitoring
    echo ""
    check_infrastructure
    echo ""
    check_scripts
    echo ""
    check_mission
    echo ""
    check_configuration
    echo ""
    check_system_dependencies
    echo ""
    check_retreat_integration
    echo ""
    
    # Génération du rapport
    generate_health_report
    
    # Résumé final
    echo -e "${CYAN}RÉSUMÉ DE LA VÉRIFICATION:${NC}"
    echo "=========================="
    
    if [[ $ERRORS -eq 0 && $WARNINGS -eq 0 ]]; then
        echo -e "${GREEN}✅ Hanuman est parfaitement configuré !${NC}"
        exit 0
    elif [[ $ERRORS -eq 0 ]]; then
        echo -e "${YELLOW}⚠️ Hanuman est fonctionnel avec $WARNINGS avertissement(s)${NC}"
        exit 0
    else
        echo -e "${RED}❌ Hanuman a $ERRORS erreur(s) et $WARNINGS avertissement(s)${NC}"
        echo -e "${RED}Veuillez corriger les erreurs avant de démarrer Hanuman${NC}"
        exit 1
    fi
}

# Exécution du script principal
main "$@"
