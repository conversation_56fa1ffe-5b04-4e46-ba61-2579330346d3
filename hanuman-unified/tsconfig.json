{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@brain/*": ["brain/*"], "@sensory/*": ["sensory-organs/*"], "@vital/*": ["vital-organs/*"], "@immune/*": ["immune-system/*"], "@voice/*": ["voice-system/*"], "@agents/*": ["specialized-agents/*"], "@sandbox/*": ["sandbox/*"], "@monitoring/*": ["monitoring/*"], "@infrastructure/*": ["infrastructure/*"], "@mission/*": ["mission/*"], "@config/*": ["config/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"]}, "typeRoots": ["./node_modules/@types", "./types"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["**/*.ts", "**/*.tsx", "types/**/*"], "exclude": ["node_modules", "dist", "coverage", "logs", "pids", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**/*"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node", "compilerOptions": {"module": "commonjs", "target": "ES2020"}}}