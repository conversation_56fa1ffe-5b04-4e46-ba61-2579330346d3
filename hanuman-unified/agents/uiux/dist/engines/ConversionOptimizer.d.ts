import { Logger } from 'winston';
import { Persona, AdaptiveDesignSystem, ConversionWireframes, UsabilityResults, ConversionOptimizations } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Optimiseur de Conversion Scientifique
 *
 * Utilise des techniques basées sur la psychologie comportementale
 * et l'analyse de données pour maximiser les taux de conversion.
 */
export declare class ConversionOptimizer {
    private logger;
    private memory;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Génère des wireframes optimisés pour la conversion
     */
    generateWireframes(personas: Persona[], designSystem: AdaptiveDesignSystem): Promise<ConversionWireframes>;
    /**
     * Optimise scientifiquement pour la conversion
     */
    optimizeForConversion(wireframes: ConversionWireframes, usabilityResults: UsabilityResults): Promise<ConversionOptimizations>;
    private createOptimalUserFlows;
    private designHighConvertingLandingPages;
    private optimizeSignupConversionFlow;
    private designEngagingOnboarding;
    private createProductiveDashboard;
    private optimizeMobileFirst;
    private integrateAccessibilityFeatures;
    private mapConversionFunnels;
    private placeTrustSignalsOptimally;
    private integrateSocialProofElements;
    private optimizeCTAPlacement;
    private optimizeCTAWording;
    private optimizeCTADesign;
    private addUrgencyElements;
    private generateABTestVariants;
    private minimizeFormFields;
    private improveFormValidation;
    private addProgressIndicators;
    private enableIntelligentAutofill;
    private enhanceFormAccessibility;
    private createDiscoveryFlow;
    private createConsiderationFlow;
    private createBookingFlow;
    private createPostBookingFlow;
    private createCompellingHeadline;
    private createSupportingSubheadline;
    private designPrimaryCTA;
    private selectOptimalHeroImage;
    private addImmediateTrustSignals;
    private getMostCommonMotivation;
    private selectHighImpactReviews;
    private highlightKeyMetrics;
    private displayPartnerLogos;
    private showcaseUserContent;
    private addScarcityElements;
    private addGuarantees;
    private addressCommonObjections;
    private minimizeSignupSteps;
    private optimizeFormFields;
    private implementSmartValidation;
    private addProgressVisualization;
    private addSignupIncentives;
    private enableSocialAuthentication;
    private createWelcomeExperience;
    private designInteractiveTutorial;
    private addPersonalizationQuiz;
    private identifyQuickWins;
    private integrateContextualHelp;
    private optimizeDashboardLayout;
    private selectRelevantWidgets;
    private simplifyDashboardNavigation;
    private prioritizeKeyActions;
    private enableDashboardCustomization;
    private designMobileNavigation;
    private implementTouchGestures;
    private optimizeMobilePerformance;
    private addOfflineCapabilities;
    private enablePWAFeatures;
    private optimizeForScreenReaders;
    private ensureKeyboardNavigation;
    private optimizeColorContrast;
    private enableFontSizeAdjustment;
    private respectMotionPreferences;
    private simplifyForCognitiveAccessibility;
    private designAwarenessFunnel;
    private designInterestFunnel;
    private designConsiderationFunnel;
    private designIntentFunnel;
    private designPurchaseFunnel;
    private designRetentionFunnel;
    private addHeaderTrustSignals;
    private addHeroTrustSignals;
    private addCheckoutTrustSignals;
    private addFooterTrustSignals;
    private addProductTrustSignals;
    private displayCustomerReviews;
    private showcaseTestimonials;
    private highlightUsageStatistics;
    private showRecentActivity;
    private displayExpertEndorsements;
    private showcaseCommunitySize;
    private optimizeTrustSignalPlacement;
    private selectMostEffectiveTestimonials;
    private highlightRelevantCertifications;
    private integrateDynamicSocialProof;
    private createRiskReversalElements;
    private simplifyNavigation;
    private optimizeLoadingExperience;
    private implementErrorPrevention;
    private addIntelligentHelp;
}
//# sourceMappingURL=ConversionOptimizer.d.ts.map