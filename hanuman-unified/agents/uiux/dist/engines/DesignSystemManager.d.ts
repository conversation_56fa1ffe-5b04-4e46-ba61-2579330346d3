import { Logger } from 'winston';
import { DesignRequirements, Persona, DesignIntelligence, AdaptiveDesignSystem, ConversionWireframes } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Gestionnaire de Design System Adaptatif
 *
 * Crée des design systems personnalisés basés sur :
 * - Les personas utilisateur
 * - Les tendances de l'industrie
 * - Les besoins d'accessibilité
 * - Les objectifs de conversion
 */
export declare class DesignSystemManager {
    private logger;
    private memory;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Crée un design system adaptatif complet
     */
    createAdaptiveSystem(requirements: DesignRequirements, personas: Persona[], intelligence: DesignIntelligence): Promise<AdaptiveDesignSystem>;
    /**
     * Crée une bibliothèque de composants
     */
    createComponentLibrary(wireframes: ConversionWireframes, designSystem: AdaptiveDesignSystem): Promise<Record<string, any>>;
    /**
     * Crée un guide d'implémentation pour les développeurs
     */
    createImplementationGuide(designSystem: AdaptiveDesignSystem, wireframes: ConversionWireframes): Promise<string>;
    private generateColorSystem;
    private createTypographySystem;
    private createSpacingSystem;
    private createOptimizedComponents;
    private createMotionSystem;
    private generateDesignTokens;
    private generateAccessiblePrimaryPalette;
    private generateSecondaryPalette;
    private generateNeutralPalette;
    private generateDarkModeVariant;
    private selectOptimalFontPairings;
    private createResponsiveTypeScale;
    private defineTypographicHierarchy;
    private ensureTypographicAccessibility;
    private calculateOptimalBaseUnit;
    private createHarmoniousSpacingScale;
    private createResponsiveSpacing;
    private adaptDensityToAudience;
    private designConversionOptimizedButtons;
    private designAccessibleForms;
    private designIntuitiveNavigation;
    private designEngagingCards;
    private designNonIntrusiveModals;
    private defineAccessibleAnimationDurations;
    private selectPersonalityEasings;
    private createMeaningfulMotionChoreography;
    private createReducedMotionAlternatives;
    private createDeveloperGuide;
    private createButtonComponents;
    private createFormComponents;
    private createNavigationComponents;
    private createCardComponents;
    private createModalComponents;
    private createRetreatCardComponent;
    private createBookingFormComponent;
    private createFilterPanelComponent;
    private createReviewComponent;
    private createPartnerProfileComponent;
    private createCTAComponents;
    private createTrustSignalComponents;
    private createSocialProofComponents;
    private createPricingCardComponents;
    private createComponentDocumentation;
    private createStorybookConfig;
    private createFigmaTokens;
    private generateShadowTokens;
    private generateBorderTokens;
    private generateAnimationTokens;
}
//# sourceMappingURL=DesignSystemManager.d.ts.map