{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */\n@layer properties;\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.z-50 {\n  z-index: 50;\n}\n.container {\n  width: 100%;\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.mt-auto {\n  margin-top: auto;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline-block {\n  display: inline-block;\n}\n.h-full {\n  height: 100%;\n}\n.h-screen {\n  height: 100vh;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-full {\n  width: 100%;\n}\n.flex-1 {\n  flex: 1;\n}\n.scale-105 {\n  --tw-scale-x: 105%;\n  --tw-scale-y: 105%;\n  --tw-scale-z: 105%;\n  scale: var(--tw-scale-x) var(--tw-scale-y);\n}\n.transform {\n  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.appearance-none {\n  appearance: none;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-center {\n  align-items: center;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-start {\n  align-items: flex-start;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-2 {\n  border-style: var(--tw-border-style);\n  border-width: 2px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-r {\n  border-right-style: var(--tw-border-style);\n  border-right-width: 1px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-b-2 {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 2px;\n}\n.border-l-4 {\n  border-left-style: var(--tw-border-style);\n  border-left-width: 4px;\n}\n.bg-gradient-to-br {\n  --tw-gradient-position: to bottom right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  --tw-gradient-position: to right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-t {\n  --tw-gradient-position: to top in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-clip-text {\n  background-clip: text;\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.text-right {\n  text-align: right;\n}\n.text-transparent {\n  color: transparent;\n}\n.capitalize {\n  text-transform: capitalize;\n}\n.opacity-50 {\n  opacity: 50%;\n}\n.opacity-70 {\n  opacity: 70%;\n}\n.opacity-80 {\n  opacity: 80%;\n}\n.filter {\n  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n}\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n}\n.transition {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-transform {\n  transition-property: transform, translate, scale, rotate;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.delay-100 {\n  transition-delay: 100ms;\n}\n.delay-200 {\n  transition-delay: 200ms;\n}\n.duration-100 {\n  --tw-duration: 100ms;\n  transition-duration: 100ms;\n}\n.duration-200 {\n  --tw-duration: 200ms;\n  transition-duration: 200ms;\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.duration-500 {\n  --tw-duration: 500ms;\n  transition-duration: 500ms;\n}\n.duration-1000 {\n  --tw-duration: 1000ms;\n  transition-duration: 1000ms;\n}\n.hover\\:scale-105 {\n  &:hover {\n    @media (hover: hover) {\n      --tw-scale-x: 105%;\n      --tw-scale-y: 105%;\n      --tw-scale-z: 105%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n}\n.disabled\\:opacity-50 {\n  &:disabled {\n    opacity: 50%;\n  }\n}\n:root {\n  --divine-gold: #fbbf24;\n  --divine-orange: #f97316;\n  --divine-red: #dc2626;\n  --sacred-blue: #3b82f6;\n  --cosmic-purple: #8b5cf6;\n  --consciousness-dark: #1e293b;\n  --golden-ratio: 1.618;\n  --phi: 1.618rem;\n  --sacred-frequency: 432;\n  --divine-transition: 618ms cubic-bezier(0.4, 0, 0.2, 1);\n  --cosmic-transition: 1618ms cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  --divine-shadow: 0 10px 25px -3px rgba(251, 191, 36, 0.3);\n  --cosmic-shadow: 0 10px 25px -3px rgba(139, 92, 246, 0.3);\n  --blessing-glow: 0 0 20px rgba(251, 191, 36, 0.5);\n}\n[data-theme=\"dark\"] {\n  --divine-bg: #0f172a;\n  --divine-surface: #1e293b;\n  --divine-text: #f8fafc;\n}\n[data-theme=\"light\"] {\n  --divine-bg: #f8fafc;\n  --divine-surface: #ffffff;\n  --divine-text: #1e293b;\n}\n* {\n  box-sizing: border-box;\n  padding: 0;\n  margin: 0;\n}\nhtml,\nbody {\n  max-width: 100vw;\n  overflow-x: hidden;\n  font-family: 'Inter', system-ui, -apple-system, sans-serif;\n  scroll-behavior: smooth;\n}\nbody {\n  color: var(--divine-text);\n  background: var(--divine-bg);\n  transition: background-color var(--divine-transition), color var(--divine-transition);\n}\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n::-webkit-scrollbar-track {\n  background: transparent;\n}\n::-webkit-scrollbar-thumb {\n  background: var(--divine-gold);\n  border-radius: 4px;\n  transition: background var(--divine-transition);\n}\n::-webkit-scrollbar-thumb:hover {\n  background: var(--divine-orange);\n}\n::selection {\n  background: var(--divine-gold);\n  color: white;\n}\n::-moz-selection {\n  background: var(--divine-gold);\n  color: white;\n}\n:focus {\n  outline: 2px solid var(--divine-gold);\n  outline-offset: 2px;\n}\n:focus:not(:focus-visible) {\n  outline: none;\n}\na {\n  color: inherit;\n  text-decoration: none;\n  transition: all var(--divine-transition);\n}\na:hover {\n  color: var(--divine-gold);\n}\nbutton {\n  cursor: pointer;\n  transition: all var(--divine-transition);\n}\nbutton:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\nimg {\n  max-width: 100%;\n  height: auto;\n}\n@keyframes divine-pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.8;\n    transform: scale(1.05);\n  }\n}\n@keyframes cosmic-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes blessing-glow {\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(251, 191, 36, 0.6);\n  }\n}\n@keyframes consciousness-flow {\n  0%, 100% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n}\n@keyframes divine-float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n@keyframes sacred-breathe {\n  0%, 100% {\n    transform: scale(1);\n    opacity: 0.8;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 1;\n  }\n}\n.divine-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.sacred-text {\n  background: linear-gradient(135deg, var(--divine-gold), var(--divine-orange));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.cosmic-text {\n  background: linear-gradient(135deg, var(--sacred-blue), var(--cosmic-purple));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.divine-glass {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.sacred-glow {\n  filter: drop-shadow(0 0 10px rgba(251, 191, 36, 0.5));\n}\n.cosmic-glow {\n  filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5));\n}\n.divine-gradient {\n  background: linear-gradient(135deg, var(--divine-gold) 0%, var(--divine-orange) 50%, var(--divine-red) 100%);\n}\n.cosmic-gradient {\n  background: linear-gradient(135deg, var(--sacred-blue) 0%, var(--cosmic-purple) 50%, #d946ef 100%);\n}\n.consciousness-gradient {\n  background: linear-gradient(135deg, var(--consciousness-dark) 0%, #475569 50%, #64748b 100%);\n}\n.animate-divine-pulse {\n  animation: divine-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n.animate-cosmic-spin {\n  animation: cosmic-spin 3s linear infinite;\n}\n.animate-blessing-glow {\n  animation: blessing-glow 2s ease-in-out infinite alternate;\n}\n.animate-consciousness-flow {\n  background-size: 200% 200%;\n  animation: consciousness-flow 4s ease-in-out infinite;\n}\n.animate-divine-float {\n  animation: divine-float 6s ease-in-out infinite;\n}\n.animate-sacred-breathe {\n  animation: sacred-breathe 4s ease-in-out infinite;\n}\n@media (max-width: 640px) {\n  :root {\n    --phi: 1rem;\n  }\n  .divine-text-responsive {\n    font-size: clamp(1rem, 4vw, 2rem);\n  }\n}\n@media (max-width: 768px) {\n  .divine-grid-responsive {\n    grid-template-columns: 1fr;\n  }\n}\n@media (max-width: 1024px) {\n  .divine-container-responsive {\n    padding: 1rem;\n  }\n}\n@media print {\n  * {\n    background: white !important;\n    color: black !important;\n    box-shadow: none !important;\n  }\n  .no-print {\n    display: none !important;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n@media (prefers-color-scheme: dark) {\n  :root {\n    --divine-bg: #0f172a;\n    --divine-surface: #1e293b;\n    --divine-text: #f8fafc;\n  }\n}\n@media (prefers-color-scheme: light) {\n  :root {\n    --divine-bg: #f8fafc;\n    --divine-surface: #ffffff;\n    --divine-text: #1e293b;\n  }\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-border-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAgnBE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhnBJ;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAMI;EAAuB;;;;;;;;AASzB;;;;AAIF;;;;;;;;;;;;;;;;;AAgBA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAOA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAAA;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;;;;AAUA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;EACE;;;;EAGA;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;;;;AAQF;EACE;;;;;;;AAMF;EACE;;;;;;;AAMF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA"}}]}