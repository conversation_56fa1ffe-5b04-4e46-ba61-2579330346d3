(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/hanuman_central_hub.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_69a8c285._.js",
  "static/chunks/node_modules_54a37f85._.js",
  "static/chunks/hanuman_central_hub_tsx_5bf9956e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/hanuman_central_hub.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);