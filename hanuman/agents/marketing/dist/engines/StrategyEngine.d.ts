import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { MarketingStrategy } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Moteur de génération de stratégies marketing
 * Utilise l'IA pour créer des stratégies marketing personnalisées
 */
export declare class StrategyEngine extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    private readonly industryTemplates;
    constructor(logger: Logger, memory: MarketingMemory);
    /**
     * Initialise le moteur de stratégie
     */
    initialize(): Promise<void>;
    /**
     * Génère une stratégie marketing complète
     */
    generateStrategy(requirements: any, context: any): Promise<MarketingStrategy>;
    /**
     * Analyse le contexte et les exigences
     */
    private analyzeContext;
    /**
     * Génère l'audience cible
     */
    private generateTargetAudience;
    /**
     * Génère les objectifs marketing
     */
    private generateObjectives;
    /**
     * Sélectionne les canaux marketing appropriés
     */
    private selectChannels;
    /**
     * Calcule le budget marketing
     */
    private calculateBudget;
    /**
     * Crée la timeline de la stratégie
     */
    private createTimeline;
    /**
     * Définit les KPIs
     */
    private defineKPIs;
    /**
     * Valide la stratégie générée
     */
    private validateStrategy;
    private generateStrategyName;
    private generateStrategyDescription;
    private assessCompetitiveLevel;
    private identifyOpportunities;
    private extractAudienceInsights;
    private analyzeTrends;
    private generateOccupations;
    private generateValues;
    private generateInterests;
    private generateLifestyle;
    private generateBehaviors;
    private generatePainPoints;
    private generateMotivations;
    private getChannelType;
    private getChannelPlatform;
    private calculateExpectedReach;
    private calculateExpectedConversion;
    private getKPIUnit;
}
//# sourceMappingURL=StrategyEngine.d.ts.map