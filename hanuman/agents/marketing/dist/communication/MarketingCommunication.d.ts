import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { MarketingResponse } from '../types';
/**
 * Système de communication pour l'Agent Marketing
 * Utilise Kafka pour la communication synaptique avec les autres agents
 */
export declare class MarketingCommunication extends EventEmitter {
    private kafka;
    private producer;
    private consumer;
    private logger;
    private isConnected;
    private readonly topics;
    constructor(logger: Logger);
    /**
     * Initialise la communication
     */
    initialize(): Promise<void>;
    /**
     * Publie une réponse marketing
     */
    publishResponse(response: MarketingResponse): Promise<void>;
    /**
     * Notifie l'agent SEO
     */
    notifySEOAgent(payload: any): Promise<void>;
    /**
     * Notifie l'agent de traduction
     */
    notifyTranslationAgent(payload: any): Promise<void>;
    /**
     * Demande une recherche web
     */
    requestWebResearch(payload: any): Promise<any>;
    /**
     * Demande des insights UX
     */
    requestUXInsights(payload: any): Promise<any>;
    /**
     * Demande la création de contenu
     */
    requestContentCreation(payload: any): Promise<any>;
    /**
     * Souscrit aux topics Kafka
     */
    private subscribeToTopics;
    /**
     * Démarre l'écoute des messages
     */
    private startListening;
    /**
     * Traite un message reçu
     */
    private handleMessage;
    /**
     * Détermine le topic pour un type de réponse
     */
    private getTopicForResponseType;
    /**
     * Déconnexion
     */
    disconnect(): Promise<void>;
}
//# sourceMappingURL=MarketingCommunication.d.ts.map