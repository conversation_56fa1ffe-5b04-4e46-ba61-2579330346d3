import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { MarketingStrategy, Campaign, MarketingAnalytics, MarketingRequest, MarketingResponse, SocialMediaPost, AgentConfig } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
import { MarketingCommunication } from '../communication/MarketingCommunication';
/**
 * Agent Marketing Principal
 * Responsable de la stratégie marketing, gestion de campagnes et optimisation conversion
 */
export declare class MarketingAgent extends EventEmitter {
    private logger;
    private memory;
    private communication;
    private strategyEngine;
    private campaignManager;
    private conversionOptimizer;
    private abTestManager;
    private socialMediaManager;
    private analyticsEngine;
    private config;
    private isInitialized;
    constructor(config: AgentConfig, logger: Logger, memory: MarketingMemory, communication: MarketingCommunication);
    /**
     * Initialise l'agent marketing
     */
    initialize(): Promise<void>;
    /**
     * Traite une requête marketing
     */
    processRequest(request: MarketingRequest): Promise<MarketingResponse>;
    /**
     * Crée une stratégie marketing
     */
    createMarketingStrategy(requirements: any): Promise<MarketingStrategy>;
    /**
     * Lance une campagne marketing
     */
    launchCampaign(campaignData: any): Promise<Campaign>;
    /**
     * Optimise la conversion
     */
    optimizeConversion(campaignId: string): Promise<any>;
    /**
     * Gère les réseaux sociaux
     */
    manageSocialMedia(request: any): Promise<SocialMediaPost[]>;
    /**
     * Génère des analyses marketing
     */
    generateAnalytics(period: {
        start: Date;
        end: Date;
    }): Promise<MarketingAnalytics>;
    /**
     * Configuration des handlers d'événements
     */
    private setupEventHandlers;
    /**
     * Gère les requêtes de stratégie
     */
    private handleStrategyRequest;
    /**
     * Gère les requêtes de campagne
     */
    private handleCampaignRequest;
    /**
     * Gère les requêtes d'analyse
     */
    private handleAnalysisRequest;
    /**
     * Gère les requêtes d'optimisation
     */
    private handleOptimizationRequest;
    /**
     * Gère les requêtes de contenu
     */
    private handleContentRequest;
    /**
     * Analyse les exigences
     */
    private analyzeRequirements;
    /**
     * Coordonne avec les autres agents
     */
    private coordinateWithAgents;
    /**
     * Demande la création de contenu
     */
    private requestContentCreation;
    /**
     * Gère les messages de communication
     */
    private handleCommunicationMessage;
    /**
     * Gère les insights SEO
     */
    private handleSEOInsights;
    /**
     * Gère le contenu prêt
     */
    private handleContentReady;
    /**
     * Gère le feedback UX
     */
    private handleUXFeedback;
    /**
     * Arrête l'agent
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=MarketingAgent.d.ts.map