{"name": "agent-compliance", "version": "1.0.0", "description": "Agent spécialisé dans la conformité réglementaire et l'audit", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["compliance", "gdpr", "audit", "regulatory", "privacy", "security-compliance"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "kafkajs": "^2.2.4", "weaviate-ts-client": "^1.5.0", "redis": "^4.6.10", "dotenv": "^16.3.1", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.3", "crypto": "^1.0.1", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "pdf-lib": "^1.17.1", "docx": "^8.5.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}}