import { Logger } from 'winston';
import { 
  DeploymentRequest,
  DeploymentResult,
  DeployedResource
} from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as archiver from 'archiver';

/**
 * Déployeur Vercel
 * 
 * Gère les déploiements sur la plateforme Vercel avec support
 * pour les applications React, Next.js, Vue.js et sites statiques.
 */
export class VercelDeployer {
  private logger: Logger;
  private memory: WeaviateMemory;
  private apiToken: string;
  private teamId?: string;
  private baseUrl = 'https://api.vercel.com';

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.apiToken = process.env.VERCEL_TOKEN || '';
    this.teamId = process.env.VERCEL_TEAM_ID;

    if (!this.apiToken) {
      this.logger.warn('Token Vercel non configuré');
    }
  }

  /**
   * Déploie une application sur Vercel
   */
  async deploy(request: DeploymentRequest, buildResult: any): Promise<DeploymentResult> {
    this.logger.info('Déploiement Vercel', { 
      app: request.applicationName,
      environment: request.environment 
    });

    const startTime = new Date();
    const deploymentId = request.id;

    try {
      // 1. Préparer les fichiers pour le déploiement
      const deploymentFiles = await this.prepareDeploymentFiles(request, buildResult);

      // 2. Créer la configuration Vercel
      const vercelConfig = await this.generateVercelConfig(request);

      // 3. Créer le déploiement sur Vercel
      const vercelDeployment = await this.createVercelDeployment(
        request,
        deploymentFiles,
        vercelConfig
      );

      // 4. Attendre que le déploiement soit prêt
      const deploymentUrl = await this.waitForDeploymentReady(vercelDeployment.id);

      // 5. Configurer le domaine personnalisé si spécifié
      if (request.configuration.networking?.ingress?.hostname) {
        await this.configureDomain(
          vercelDeployment.id,
          request.configuration.networking.ingress.hostname
        );
      }

      // 6. Configurer les variables d'environnement
      if (Object.keys(request.configuration.environment).length > 0) {
        await this.configureEnvironmentVariables(
          request.applicationName,
          request.configuration.environment
        );
      }

      const result: DeploymentResult = {
        id: deploymentId,
        status: 'running',
        url: deploymentUrl,
        endpoints: [{
          name: 'main',
          url: deploymentUrl,
          type: 'web',
          status: 'healthy'
        }],
        resources: [{
          type: 'deployment',
          name: request.applicationName,
          status: 'running',
          created: new Date(),
          metadata: {
            vercelId: vercelDeployment.id,
            vercelUrl: deploymentUrl
          }
        }],
        metrics: await this.collectInitialMetrics(vercelDeployment.id),
        logs: [],
        startedAt: startTime,
        completedAt: new Date(),
        duration: Date.now() - startTime.getTime()
      };

      this.logger.info('Déploiement Vercel réussi', { 
        deploymentId,
        url: result.url,
        duration: result.duration 
      });

      return result;

    } catch (error) {
      this.logger.error('Erreur lors du déploiement Vercel', { 
        deploymentId,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Supprime un déploiement Vercel
   */
  async delete(deploymentId: string): Promise<void> {
    this.logger.info('Suppression du déploiement Vercel', { deploymentId });

    try {
      await this.makeVercelRequest('DELETE', `/v13/deployments/${deploymentId}`);
      this.logger.info('Déploiement Vercel supprimé', { deploymentId });
    } catch (error) {
      this.logger.error('Erreur lors de la suppression Vercel', { 
        deploymentId,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Obtient les métriques d'un déploiement
   */
  async getMetrics(deploymentId: string): Promise<any> {
    try {
      const response = await this.makeVercelRequest('GET', `/v2/deployments/${deploymentId}/events`);
      return this.parseMetrics(response.data);
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques Vercel', { 
        deploymentId,
        error: error.message 
      });
      return this.getEmptyMetrics();
    }
  }

  // Méthodes privées

  private async prepareDeploymentFiles(request: DeploymentRequest, buildResult: any): Promise<any[]> {
    const files = [];

    if (request.source.type === 'generated-code' && request.source.generatedCode) {
      // Utiliser le code généré par l'Agent Frontend
      for (const file of request.source.generatedCode.files) {
        files.push({
          file: file.path,
          data: Buffer.from(file.content).toString('base64')
        });
      }

      // Ajouter package.json si nécessaire
      if (!files.find(f => f.file === 'package.json')) {
        const packageJson = this.generatePackageJson(request);
        files.push({
          file: 'package.json',
          data: Buffer.from(JSON.stringify(packageJson, null, 2)).toString('base64')
        });
      }
    } else {
      // Utiliser les fichiers du build
      files.push(...buildResult.files || []);
    }

    return files;
  }

  private async generateVercelConfig(request: DeploymentRequest): Promise<any> {
    const config: any = {
      name: request.applicationName,
      version: 2,
      builds: [],
      routes: []
    };

    // Configuration selon le framework
    if (request.source.generatedCode?.framework === 'react') {
      config.builds.push({
        src: 'package.json',
        use: '@vercel/static-build',
        config: {
          distDir: 'build'
        }
      });
    } else if (request.source.generatedCode?.framework === 'vue') {
      config.builds.push({
        src: 'package.json',
        use: '@vercel/static-build',
        config: {
          distDir: 'dist'
        }
      });
    } else {
      // Configuration par défaut pour les sites statiques
      config.builds.push({
        src: '**/*',
        use: '@vercel/static'
      });
    }

    // Configuration des routes
    config.routes.push({
      src: '/(.*)',
      dest: '/index.html'
    });

    // Variables d'environnement
    if (Object.keys(request.configuration.environment).length > 0) {
      config.env = request.configuration.environment;
    }

    // Configuration des headers
    if (request.configuration.networking) {
      config.headers = [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff'
            },
            {
              key: 'X-Frame-Options',
              value: 'DENY'
            },
            {
              key: 'X-XSS-Protection',
              value: '1; mode=block'
            }
          ]
        }
      ];
    }

    return config;
  }

  private async createVercelDeployment(
    request: DeploymentRequest,
    files: any[],
    config: any
  ): Promise<any> {
    const deploymentData = {
      name: request.applicationName,
      files,
      projectSettings: config,
      target: request.environment === 'production' ? 'production' : 'preview',
      meta: {
        deployedBy: 'agent-devops',
        environment: request.environment,
        version: request.version
      }
    };

    const response = await this.makeVercelRequest('POST', '/v13/deployments', deploymentData);
    return response.data;
  }

  private async waitForDeploymentReady(vercelDeploymentId: string): Promise<string> {
    const maxWaitTime = 600000; // 10 minutes
    const checkInterval = 10000; // 10 secondes
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await this.makeVercelRequest('GET', `/v13/deployments/${vercelDeploymentId}`);
        const deployment = response.data;

        if (deployment.readyState === 'READY') {
          this.logger.info('Déploiement Vercel prêt', { 
            vercelDeploymentId,
            url: deployment.url 
          });
          return `https://${deployment.url}`;
        } else if (deployment.readyState === 'ERROR') {
          throw new Error(`Déploiement Vercel échoué: ${deployment.errorMessage || 'Erreur inconnue'}`);
        }

        this.logger.info('Attente du déploiement Vercel', { 
          vercelDeploymentId,
          state: deployment.readyState 
        });

        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        if (error.response?.status === 404) {
          this.logger.warn('Déploiement Vercel non trouvé', { vercelDeploymentId });
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          continue;
        }
        throw error;
      }
    }

    throw new Error(`Timeout: Le déploiement Vercel ${vercelDeploymentId} n'est pas prêt après ${maxWaitTime}ms`);
  }

  private async configureDomain(deploymentId: string, domain: string): Promise<void> {
    try {
      this.logger.info('Configuration du domaine Vercel', { deploymentId, domain });

      // Ajouter le domaine au projet
      await this.makeVercelRequest('POST', '/v9/projects/domains', {
        name: domain,
        projectId: deploymentId
      });

      // Assigner le domaine au déploiement
      await this.makeVercelRequest('POST', `/v2/domains/${domain}/records`, {
        type: 'ALIAS',
        name: '',
        value: 'alias.vercel-dns.com'
      });

      this.logger.info('Domaine Vercel configuré', { deploymentId, domain });
    } catch (error) {
      this.logger.warn('Erreur lors de la configuration du domaine Vercel', { 
        deploymentId,
        domain,
        error: error.message 
      });
    }
  }

  private async configureEnvironmentVariables(
    projectName: string,
    environment: Record<string, string>
  ): Promise<void> {
    try {
      this.logger.info('Configuration des variables d\'environnement Vercel', { projectName });

      for (const [key, value] of Object.entries(environment)) {
        await this.makeVercelRequest('POST', `/v9/projects/${projectName}/env`, {
          key,
          value,
          type: 'encrypted',
          target: ['production', 'preview', 'development']
        });
      }

      this.logger.info('Variables d\'environnement Vercel configurées', { projectName });
    } catch (error) {
      this.logger.warn('Erreur lors de la configuration des variables d\'environnement Vercel', { 
        projectName,
        error: error.message 
      });
    }
  }

  private async collectInitialMetrics(vercelDeploymentId: string): Promise<any> {
    // Collecter les métriques initiales depuis Vercel
    try {
      const metrics = await this.getMetrics(vercelDeploymentId);
      return metrics;
    } catch (error) {
      return this.getEmptyMetrics();
    }
  }

  private generatePackageJson(request: DeploymentRequest): any {
    const framework = request.source.generatedCode?.framework || 'react';
    
    const packageJson: any = {
      name: request.applicationName,
      version: request.version,
      private: true,
      scripts: {},
      dependencies: request.source.generatedCode?.dependencies.production || {},
      devDependencies: request.source.generatedCode?.dependencies.development || {}
    };

    // Scripts selon le framework
    switch (framework) {
      case 'react':
        packageJson.scripts = {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject'
        };
        break;
      case 'vue':
        packageJson.scripts = {
          serve: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
          lint: 'vue-cli-service lint'
        };
        break;
      default:
        packageJson.scripts = {
          build: 'echo "No build script defined"',
          start: 'echo "No start script defined"'
        };
    }

    return packageJson;
  }

  private async makeVercelRequest(method: string, endpoint: string, data?: any): Promise<any> {
    const config: any = {
      method,
      url: `${this.baseUrl}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json'
      }
    };

    if (this.teamId) {
      config.headers['X-Vercel-Team-Id'] = this.teamId;
    }

    if (data) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return response;
    } catch (error) {
      this.logger.error('Erreur API Vercel', { 
        method,
        endpoint,
        status: error.response?.status,
        message: error.response?.data?.error?.message || error.message 
      });
      throw error;
    }
  }

  private parseMetrics(events: any[]): any {
    // Parser les événements Vercel pour extraire les métriques
    const metrics = this.getEmptyMetrics();

    for (const event of events) {
      if (event.type === 'request') {
        metrics.requests.total++;
        if (event.payload?.statusCode >= 400) {
          metrics.errors.total++;
        }
      }
    }

    return metrics;
  }

  private getEmptyMetrics(): any {
    return {
      cpu: { current: 0, average: 0, peak: 0, unit: 'cores' },
      memory: { current: 0, average: 0, peak: 0, unit: 'MB' },
      network: { bytesIn: 0, bytesOut: 0, connectionsActive: 0, connectionsTotal: 0 },
      requests: { total: 0, rate: 0, latencyP50: 0, latencyP95: 0, latencyP99: 0 },
      errors: { total: 0, rate: 0, types: {} }
    };
  }
}
