import { Logger } from 'winston';
import { DeploymentRequest, DeploymentResult, DeploymentStatus } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import AWS from 'aws-sdk';

/**
 * Déployeur AWS
 * 
 * Gère les déploiements sur AWS avec ECS, Lambda, Elastic Beanstalk
 */
export class AWSDeployer {
  private logger: Logger;
  private memory: WeaviateMemory;
  private ecs: AWS.ECS;
  private lambda: AWS.Lambda;
  private elasticBeanstalk: AWS.ElasticBeanstalk;
  private cloudFormation: AWS.CloudFormation;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;

    // Initialiser les services AWS
    AWS.config.update({
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    });

    this.ecs = new AWS.ECS();
    this.lambda = new AWS.Lambda();
    this.elasticBeanstalk = new AWS.ElasticBeanstalk();
    this.cloudFormation = new AWS.CloudFormation();
  }

  /**
   * Déploie une application sur AWS
   */
  async deploy(request: DeploymentRequest, buildResult: any): Promise<DeploymentResult> {
    this.logger.info('Déploiement AWS', { 
      id: request.id,
      service: this.getAWSService(request) 
    });

    const startTime = new Date();
    const deploymentResult: DeploymentResult = {
      id: request.id,
      status: 'deploying',
      endpoints: [],
      resources: [],
      metrics: this.getEmptyMetrics(),
      logs: [],
      startedAt: startTime
    };

    try {
      // Déterminer le service AWS à utiliser
      const awsService = this.getAWSService(request);

      switch (awsService) {
        case 'ecs':
          return await this.deployToECS(request, buildResult, deploymentResult);
        case 'lambda':
          return await this.deployToLambda(request, buildResult, deploymentResult);
        case 'elasticbeanstalk':
          return await this.deployToElasticBeanstalk(request, buildResult, deploymentResult);
        default:
          throw new Error(`Service AWS ${awsService} non supporté`);
      }

    } catch (error) {
      this.logger.error('Erreur lors du déploiement AWS', { 
        id: request.id,
        error: error.message 
      });

      deploymentResult.status = 'failed';
      deploymentResult.error = {
        code: 'AWS_DEPLOYMENT_FAILED',
        message: error.message,
        recoverable: true
      };
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - startTime.getTime();

      throw error;
    }
  }

  /**
   * Déploiement sur ECS (Elastic Container Service)
   */
  private async deployToECS(
    request: DeploymentRequest,
    buildResult: any,
    deploymentResult: DeploymentResult
  ): Promise<DeploymentResult> {
    this.logger.info('Déploiement ECS', { id: request.id });

    try {
      // 1. Créer ou mettre à jour la définition de tâche
      const taskDefinition = await this.createTaskDefinition(request);

      // 2. Créer ou mettre à jour le service ECS
      const service = await this.createOrUpdateECSService(request, taskDefinition);

      // 3. Attendre que le déploiement soit stable
      await this.waitForECSDeployment(service.serviceName!, request.platform.cluster!);

      // 4. Configurer les endpoints
      const endpoints = await this.getECSEndpoints(service, request);

      deploymentResult.status = 'running';
      deploymentResult.url = endpoints[0]?.url;
      deploymentResult.endpoints = endpoints;
      deploymentResult.resources = [
        {
          type: 'service',
          name: service.serviceName!,
          status: 'running',
          created: new Date(),
          metadata: { cluster: request.platform.cluster }
        }
      ];
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - deploymentResult.startedAt.getTime();

      return deploymentResult;

    } catch (error) {
      this.logger.error('Erreur ECS', { error: error.message });
      throw error;
    }
  }

  /**
   * Déploiement sur Lambda
   */
  private async deployToLambda(
    request: DeploymentRequest,
    buildResult: any,
    deploymentResult: DeploymentResult
  ): Promise<DeploymentResult> {
    this.logger.info('Déploiement Lambda', { id: request.id });

    try {
      // 1. Créer ou mettre à jour la fonction Lambda
      const functionName = `${request.applicationName}-${request.environment}`;
      
      const lambdaFunction = await this.createOrUpdateLambdaFunction(
        functionName,
        request,
        buildResult
      );

      // 2. Configurer les triggers et permissions
      await this.configureLambdaTriggers(lambdaFunction, request);

      // 3. Créer l'endpoint API Gateway si nécessaire
      const apiEndpoint = await this.createAPIGatewayEndpoint(lambdaFunction, request);

      deploymentResult.status = 'running';
      deploymentResult.url = apiEndpoint;
      deploymentResult.endpoints = [
        {
          name: 'api',
          url: apiEndpoint,
          type: 'api',
          status: 'healthy'
        }
      ];
      deploymentResult.resources = [
        {
          type: 'function',
          name: functionName,
          status: 'active',
          created: new Date(),
          metadata: { runtime: 'nodejs18.x' }
        }
      ];
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - deploymentResult.startedAt.getTime();

      return deploymentResult;

    } catch (error) {
      this.logger.error('Erreur Lambda', { error: error.message });
      throw error;
    }
  }

  /**
   * Déploiement sur Elastic Beanstalk
   */
  private async deployToElasticBeanstalk(
    request: DeploymentRequest,
    buildResult: any,
    deploymentResult: DeploymentResult
  ): Promise<DeploymentResult> {
    this.logger.info('Déploiement Elastic Beanstalk', { id: request.id });

    try {
      // 1. Créer l'application si elle n'existe pas
      const applicationName = request.applicationName;
      await this.createEBApplicationIfNotExists(applicationName);

      // 2. Créer une nouvelle version de l'application
      const versionLabel = `v${request.version}-${Date.now()}`;
      await this.createApplicationVersion(applicationName, versionLabel, buildResult);

      // 3. Déployer sur l'environnement
      const environmentName = `${applicationName}-${request.environment}`;
      await this.deployToEBEnvironment(applicationName, environmentName, versionLabel, request);

      // 4. Attendre que l'environnement soit prêt
      const environment = await this.waitForEBEnvironment(applicationName, environmentName);

      deploymentResult.status = 'running';
      deploymentResult.url = environment.CNAME ? `http://${environment.CNAME}` : undefined;
      deploymentResult.endpoints = [
        {
          name: 'web',
          url: `http://${environment.CNAME}`,
          type: 'web',
          status: 'healthy'
        }
      ];
      deploymentResult.resources = [
        {
          type: 'environment',
          name: environmentName,
          status: environment.Status!,
          created: new Date(),
          metadata: { platform: environment.PlatformArn }
        }
      ];
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - deploymentResult.startedAt.getTime();

      return deploymentResult;

    } catch (error) {
      this.logger.error('Erreur Elastic Beanstalk', { error: error.message });
      throw error;
    }
  }

  // Méthodes utilitaires

  private getAWSService(request: DeploymentRequest): string {
    // Déterminer le service AWS basé sur la configuration
    const config = request.configuration;
    
    if (config.environment.AWS_SERVICE) {
      return config.environment.AWS_SERVICE.toLowerCase();
    }

    // Heuristiques basées sur le type d'application
    if (request.source.type === 'docker') {
      return 'ecs';
    }
    
    if (request.applicationName.includes('lambda') || 
        request.applicationName.includes('function')) {
      return 'lambda';
    }

    return 'elasticbeanstalk'; // Par défaut
  }

  private async createTaskDefinition(request: DeploymentRequest): Promise<AWS.ECS.TaskDefinition> {
    const taskDefParams: AWS.ECS.RegisterTaskDefinitionRequest = {
      family: `${request.applicationName}-${request.environment}`,
      networkMode: 'awsvpc',
      requiresCompatibilities: ['FARGATE'],
      cpu: request.configuration.resources?.cpu.request || '256',
      memory: request.configuration.resources?.memory.request || '512',
      executionRoleArn: process.env.ECS_EXECUTION_ROLE_ARN,
      containerDefinitions: [
        {
          name: request.applicationName,
          image: `${request.applicationName}:${request.version}`,
          portMappings: request.configuration.networking?.ports.map(p => ({
            containerPort: p.targetPort,
            protocol: p.protocol.toLowerCase() as 'tcp' | 'udp'
          })) || [{ containerPort: 3000, protocol: 'tcp' }],
          environment: Object.entries(request.configuration.environment).map(([name, value]) => ({
            name,
            value
          })),
          logConfiguration: {
            logDriver: 'awslogs',
            options: {
              'awslogs-group': `/ecs/${request.applicationName}`,
              'awslogs-region': process.env.AWS_REGION || 'us-east-1',
              'awslogs-stream-prefix': 'ecs'
            }
          }
        }
      ]
    };

    const result = await this.ecs.registerTaskDefinition(taskDefParams).promise();
    return result.taskDefinition!;
  }

  private async createOrUpdateECSService(
    request: DeploymentRequest,
    taskDefinition: AWS.ECS.TaskDefinition
  ): Promise<AWS.ECS.Service> {
    const serviceName = `${request.applicationName}-${request.environment}`;
    const clusterName = request.platform.cluster || 'default';

    try {
      // Essayer de mettre à jour le service existant
      const updateParams: AWS.ECS.UpdateServiceRequest = {
        cluster: clusterName,
        service: serviceName,
        taskDefinition: taskDefinition.taskDefinitionArn!,
        desiredCount: request.configuration.replicas || 1
      };

      const result = await this.ecs.updateService(updateParams).promise();
      return result.service!;

    } catch (error) {
      // Si le service n'existe pas, le créer
      const createParams: AWS.ECS.CreateServiceRequest = {
        cluster: clusterName,
        serviceName,
        taskDefinition: taskDefinition.taskDefinitionArn!,
        desiredCount: request.configuration.replicas || 1,
        launchType: 'FARGATE',
        networkConfiguration: {
          awsvpcConfiguration: {
            subnets: process.env.ECS_SUBNETS?.split(',') || [],
            securityGroups: process.env.ECS_SECURITY_GROUPS?.split(',') || [],
            assignPublicIp: 'ENABLED'
          }
        }
      };

      const result = await this.ecs.createService(createParams).promise();
      return result.service!;
    }
  }

  private async waitForECSDeployment(serviceName: string, clusterName: string): Promise<void> {
    this.logger.info('Attente de la stabilité du service ECS');
    
    await this.ecs.waitFor('servicesStable', {
      cluster: clusterName,
      services: [serviceName]
    }).promise();
  }

  private async getECSEndpoints(service: AWS.ECS.Service, request: DeploymentRequest): Promise<any[]> {
    // Récupérer les endpoints du load balancer si configuré
    return [
      {
        name: 'web',
        url: `http://${request.applicationName}-${request.environment}.example.com`,
        type: 'web',
        status: 'healthy'
      }
    ];
  }

  private async createOrUpdateLambdaFunction(
    functionName: string,
    request: DeploymentRequest,
    buildResult: any
  ): Promise<AWS.Lambda.FunctionConfiguration> {
    const functionParams: AWS.Lambda.CreateFunctionRequest = {
      FunctionName: functionName,
      Runtime: 'nodejs18.x',
      Role: process.env.LAMBDA_EXECUTION_ROLE_ARN!,
      Handler: 'index.handler',
      Code: {
        ZipFile: Buffer.from('exports.handler = async (event) => ({ statusCode: 200, body: "Hello World" });')
      },
      Environment: {
        Variables: request.configuration.environment
      },
      Timeout: 30,
      MemorySize: 128
    };

    try {
      const result = await this.lambda.createFunction(functionParams).promise();
      return result;
    } catch (error) {
      if (error.code === 'ResourceConflictException') {
        // Fonction existe déjà, la mettre à jour
        const updateParams: AWS.Lambda.UpdateFunctionCodeRequest = {
          FunctionName: functionName,
          ZipFile: functionParams.Code.ZipFile
        };
        
        await this.lambda.updateFunctionCode(updateParams).promise();
        return await this.lambda.getFunction({ FunctionName: functionName }).promise().then(r => r.Configuration!);
      }
      throw error;
    }
  }

  private async configureLambdaTriggers(
    lambdaFunction: AWS.Lambda.FunctionConfiguration,
    request: DeploymentRequest
  ): Promise<void> {
    // Configurer les triggers (API Gateway, S3, etc.)
    this.logger.info('Configuration des triggers Lambda');
  }

  private async createAPIGatewayEndpoint(
    lambdaFunction: AWS.Lambda.FunctionConfiguration,
    request: DeploymentRequest
  ): Promise<string> {
    // Créer un endpoint API Gateway
    return `https://api.example.com/${request.applicationName}`;
  }

  private async createEBApplicationIfNotExists(applicationName: string): Promise<void> {
    try {
      await this.elasticBeanstalk.createApplication({
        ApplicationName: applicationName,
        Description: `Application ${applicationName} créée automatiquement`
      }).promise();
    } catch (error) {
      if (error.code !== 'InvalidParameterValue') {
        throw error;
      }
    }
  }

  private async createApplicationVersion(
    applicationName: string,
    versionLabel: string,
    buildResult: any
  ): Promise<void> {
    await this.elasticBeanstalk.createApplicationVersion({
      ApplicationName: applicationName,
      VersionLabel: versionLabel,
      Description: `Version ${versionLabel}`,
      SourceBundle: {
        S3Bucket: process.env.EB_SOURCE_BUCKET!,
        S3Key: `${applicationName}/${versionLabel}.zip`
      }
    }).promise();
  }

  private async deployToEBEnvironment(
    applicationName: string,
    environmentName: string,
    versionLabel: string,
    request: DeploymentRequest
  ): Promise<void> {
    try {
      await this.elasticBeanstalk.updateEnvironment({
        ApplicationName: applicationName,
        EnvironmentName: environmentName,
        VersionLabel: versionLabel
      }).promise();
    } catch (error) {
      if (error.code === 'InvalidParameterValue') {
        // Environnement n'existe pas, le créer
        await this.elasticBeanstalk.createEnvironment({
          ApplicationName: applicationName,
          EnvironmentName: environmentName,
          VersionLabel: versionLabel,
          SolutionStackName: '64bit Amazon Linux 2 v5.8.0 running Node.js 18'
        }).promise();
      } else {
        throw error;
      }
    }
  }

  private async waitForEBEnvironment(
    applicationName: string,
    environmentName: string
  ): Promise<AWS.ElasticBeanstalk.EnvironmentDescription> {
    this.logger.info('Attente de la disponibilité de l\'environnement EB');
    
    await this.elasticBeanstalk.waitFor('environmentUpdated', {
      ApplicationName: applicationName,
      EnvironmentNames: [environmentName]
    }).promise();

    const result = await this.elasticBeanstalk.describeEnvironments({
      ApplicationName: applicationName,
      EnvironmentNames: [environmentName]
    }).promise();

    return result.Environments![0];
  }

  private getEmptyMetrics(): any {
    return {
      cpu: { current: 0, average: 0, peak: 0, unit: 'cores' },
      memory: { current: 0, average: 0, peak: 0, unit: 'MB' },
      network: { bytesIn: 0, bytesOut: 0, connectionsActive: 0, connectionsTotal: 0 },
      requests: { total: 0, rate: 0, latencyP50: 0, latencyP95: 0, latencyP99: 0 },
      errors: { total: 0, rate: 0, types: {} }
    };
  }
}
