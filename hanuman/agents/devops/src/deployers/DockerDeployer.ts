import { Logger } from 'winston';
import { DeploymentRequest, DeploymentResult, DeploymentStatus } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import Docker from 'dockerode';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Déployeur Docker
 * 
 * Gère les déploiements avec Docker et Docker Compose
 */
export class DockerDeployer {
  private logger: Logger;
  private memory: WeaviateMemory;
  private docker: Docker;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;

    // Initialiser le client Docker
    this.docker = new Docker({
      socketPath: process.env.DOCKER_SOCKET_PATH || '/var/run/docker.sock'
    });
  }

  /**
   * Déploie une application avec Docker
   */
  async deploy(request: DeploymentRequest, buildResult: any): Promise<DeploymentResult> {
    this.logger.info('Déploiement Docker', { 
      id: request.id,
      image: buildResult.image 
    });

    const startTime = new Date();
    const deploymentResult: DeploymentResult = {
      id: request.id,
      status: 'deploying',
      endpoints: [],
      resources: [],
      metrics: this.getEmptyMetrics(),
      logs: [],
      startedAt: startTime
    };

    try {
      // Déterminer le type de déploiement
      if (request.configuration.docker?.compose) {
        return await this.deployWithCompose(request, buildResult, deploymentResult);
      } else {
        return await this.deployWithDocker(request, buildResult, deploymentResult);
      }

    } catch (error) {
      this.logger.error('Erreur lors du déploiement Docker', { 
        id: request.id,
        error: error.message 
      });

      deploymentResult.status = 'failed';
      deploymentResult.error = {
        code: 'DOCKER_DEPLOYMENT_FAILED',
        message: error.message,
        recoverable: true
      };
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - startTime.getTime();

      throw error;
    }
  }

  /**
   * Déploiement avec Docker simple
   */
  private async deployWithDocker(
    request: DeploymentRequest,
    buildResult: any,
    deploymentResult: DeploymentResult
  ): Promise<DeploymentResult> {
    this.logger.info('Déploiement Docker simple', { id: request.id });

    try {
      // 1. Arrêter et supprimer le conteneur existant s'il existe
      await this.stopExistingContainer(request.applicationName);

      // 2. Créer et démarrer le nouveau conteneur
      const container = await this.createContainer(request, buildResult);
      await container.start();

      // 3. Attendre que le conteneur soit prêt
      await this.waitForContainerReady(container, request);

      // 4. Récupérer les informations du conteneur
      const containerInfo = await container.inspect();
      const endpoints = this.getContainerEndpoints(containerInfo, request);

      deploymentResult.status = 'running';
      deploymentResult.url = endpoints[0]?.url;
      deploymentResult.endpoints = endpoints;
      deploymentResult.resources = [
        {
          type: 'container',
          name: containerInfo.Name.replace('/', ''),
          status: containerInfo.State.Status,
          created: new Date(containerInfo.Created),
          metadata: {
            id: containerInfo.Id,
            image: containerInfo.Config.Image,
            ports: containerInfo.NetworkSettings.Ports
          }
        }
      ];
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - deploymentResult.startedAt.getTime();

      return deploymentResult;

    } catch (error) {
      this.logger.error('Erreur déploiement Docker simple', { error: error.message });
      throw error;
    }
  }

  /**
   * Déploiement avec Docker Compose
   */
  private async deployWithCompose(
    request: DeploymentRequest,
    buildResult: any,
    deploymentResult: DeploymentResult
  ): Promise<DeploymentResult> {
    this.logger.info('Déploiement Docker Compose', { id: request.id });

    try {
      // 1. Générer le fichier docker-compose.yml
      const composeFile = await this.generateComposeFile(request, buildResult);
      const composeFilePath = path.join(process.cwd(), 'temp', `${request.id}-docker-compose.yml`);
      
      await fs.ensureDir(path.dirname(composeFilePath));
      await fs.writeFile(composeFilePath, composeFile);

      // 2. Arrêter les services existants
      await this.executeCommand(`docker-compose -f ${composeFilePath} down`);

      // 3. Démarrer les services
      await this.executeCommand(`docker-compose -f ${composeFilePath} up -d`);

      // 4. Attendre que les services soient prêts
      await this.waitForComposeServices(composeFilePath, request);

      // 5. Récupérer les informations des services
      const services = await this.getComposeServices(composeFilePath);
      const endpoints = this.getComposeEndpoints(services, request);

      deploymentResult.status = 'running';
      deploymentResult.url = endpoints[0]?.url;
      deploymentResult.endpoints = endpoints;
      deploymentResult.resources = services.map(service => ({
        type: 'service',
        name: service.name,
        status: service.status,
        created: new Date(),
        metadata: {
          compose: true,
          ports: service.ports
        }
      }));
      deploymentResult.completedAt = new Date();
      deploymentResult.duration = Date.now() - deploymentResult.startedAt.getTime();

      // Nettoyer le fichier temporaire
      await fs.remove(composeFilePath);

      return deploymentResult;

    } catch (error) {
      this.logger.error('Erreur déploiement Docker Compose', { error: error.message });
      throw error;
    }
  }

  /**
   * Arrête un conteneur existant
   */
  private async stopExistingContainer(containerName: string): Promise<void> {
    try {
      const containers = await this.docker.listContainers({ all: true });
      const existingContainer = containers.find(c => 
        c.Names.some(name => name.includes(containerName))
      );

      if (existingContainer) {
        this.logger.info(`Arrêt du conteneur existant ${containerName}`);
        const container = this.docker.getContainer(existingContainer.Id);
        
        if (existingContainer.State === 'running') {
          await container.stop();
        }
        await container.remove();
      }
    } catch (error) {
      this.logger.warn(`Erreur lors de l'arrêt du conteneur ${containerName}`, { 
        error: error.message 
      });
    }
  }

  /**
   * Crée un nouveau conteneur
   */
  private async createContainer(request: DeploymentRequest, buildResult: any): Promise<Docker.Container> {
    const containerConfig = {
      Image: buildResult.image || `${request.applicationName}:${request.version}`,
      name: `${request.applicationName}-${request.environment}`,
      Env: Object.entries(request.configuration.environment).map(([key, value]) => `${key}=${value}`),
      ExposedPorts: this.getExposedPorts(request),
      HostConfig: {
        PortBindings: this.getPortBindings(request),
        RestartPolicy: { Name: 'unless-stopped' },
        Memory: this.parseMemory(request.configuration.resources?.memory.limit),
        CpuShares: this.parseCpu(request.configuration.resources?.cpu.limit)
      },
      Labels: {
        'managed-by': 'agent-devops',
        'app': request.applicationName,
        'version': request.version,
        'environment': request.environment
      }
    };

    return await this.docker.createContainer(containerConfig);
  }

  /**
   * Attend que le conteneur soit prêt
   */
  private async waitForContainerReady(container: Docker.Container, request: DeploymentRequest): Promise<void> {
    this.logger.info('Attente de la disponibilité du conteneur');
    
    const maxAttempts = 30; // 30 secondes
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const containerInfo = await container.inspect();
        
        if (containerInfo.State.Status === 'running') {
          // Vérifier la santé si un health check est configuré
          if (request.configuration.monitoring?.healthChecks?.liveness) {
            const isHealthy = await this.checkContainerHealth(container, request);
            if (isHealthy) {
              this.logger.info('Conteneur prêt et en bonne santé');
              return;
            }
          } else {
            this.logger.info('Conteneur en cours d\'exécution');
            return;
          }
        }

        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        this.logger.warn('Erreur lors de la vérification du conteneur', { 
          error: error.message 
        });
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('Timeout: Le conteneur n\'est pas devenu prêt dans les temps');
  }

  /**
   * Vérifie la santé du conteneur
   */
  private async checkContainerHealth(container: Docker.Container, request: DeploymentRequest): Promise<boolean> {
    try {
      const healthCheck = request.configuration.monitoring?.healthChecks?.liveness;
      if (!healthCheck) return true;

      const containerInfo = await container.inspect();
      const ports = containerInfo.NetworkSettings.Ports;
      
      // Trouver le port mappé
      const portKey = `${healthCheck.port}/tcp`;
      const mappedPort = ports[portKey]?.[0]?.HostPort;
      
      if (!mappedPort) return true; // Pas de port mappé, considérer comme sain

      // Faire un appel HTTP au health check
      const axios = require('axios');
      const healthUrl = `http://localhost:${mappedPort}${healthCheck.path}`;
      
      const response = await axios.get(healthUrl, { timeout: 5000 });
      return response.status >= 200 && response.status < 300;

    } catch (error) {
      this.logger.debug('Health check échoué', { error: error.message });
      return false;
    }
  }

  /**
   * Récupère les endpoints du conteneur
   */
  private getContainerEndpoints(containerInfo: any, request: DeploymentRequest): any[] {
    const endpoints = [];
    const ports = containerInfo.NetworkSettings.Ports;

    for (const [containerPort, hostPorts] of Object.entries(ports)) {
      if (hostPorts && Array.isArray(hostPorts)) {
        for (const hostPort of hostPorts) {
          endpoints.push({
            name: `port-${containerPort.replace('/tcp', '')}`,
            url: `http://localhost:${hostPort.HostPort}`,
            type: 'web',
            status: 'healthy'
          });
        }
      }
    }

    return endpoints;
  }

  /**
   * Génère un fichier docker-compose.yml
   */
  private async generateComposeFile(request: DeploymentRequest, buildResult: any): Promise<string> {
    const services: any = {};
    
    // Service principal
    services[request.applicationName] = {
      image: buildResult.image || `${request.applicationName}:${request.version}`,
      container_name: `${request.applicationName}-${request.environment}`,
      environment: request.configuration.environment,
      ports: request.configuration.networking?.ports.map(p => `${p.port}:${p.targetPort}`) || ['3000:3000'],
      restart: 'unless-stopped',
      labels: {
        'managed-by': 'agent-devops',
        'app': request.applicationName,
        'version': request.version
      }
    };

    // Ajouter des ressources si spécifiées
    if (request.configuration.resources) {
      services[request.applicationName].deploy = {
        resources: {
          limits: {
            memory: request.configuration.resources.memory.limit,
            cpus: request.configuration.resources.cpu.limit
          },
          reservations: {
            memory: request.configuration.resources.memory.request,
            cpus: request.configuration.resources.cpu.request
          }
        }
      };
    }

    // Ajouter des services dépendants (base de données, cache, etc.)
    if (request.configuration.docker?.compose?.services) {
      Object.assign(services, request.configuration.docker.compose.services);
    }

    const compose = {
      version: '3.8',
      services,
      networks: {
        default: {
          name: `${request.applicationName}-network`
        }
      }
    };

    return require('yaml').stringify(compose);
  }

  /**
   * Attend que les services Compose soient prêts
   */
  private async waitForComposeServices(composeFilePath: string, request: DeploymentRequest): Promise<void> {
    this.logger.info('Attente des services Docker Compose');
    
    const maxAttempts = 60; // 1 minute
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const output = await this.executeCommand(`docker-compose -f ${composeFilePath} ps --format json`);
        const services = JSON.parse(output);
        
        const allRunning = services.every((service: any) => service.State === 'running');
        
        if (allRunning) {
          this.logger.info('Tous les services Docker Compose sont prêts');
          return;
        }

        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        this.logger.warn('Erreur lors de la vérification des services Compose', { 
          error: error.message 
        });
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('Timeout: Les services Docker Compose ne sont pas devenus prêts dans les temps');
  }

  /**
   * Récupère les services Compose
   */
  private async getComposeServices(composeFilePath: string): Promise<any[]> {
    try {
      const output = await this.executeCommand(`docker-compose -f ${composeFilePath} ps --format json`);
      return JSON.parse(output);
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des services Compose', { 
        error: error.message 
      });
      return [];
    }
  }

  /**
   * Récupère les endpoints des services Compose
   */
  private getComposeEndpoints(services: any[], request: DeploymentRequest): any[] {
    const endpoints = [];

    for (const service of services) {
      if (service.Publishers) {
        for (const publisher of service.Publishers) {
          endpoints.push({
            name: service.Service,
            url: `http://localhost:${publisher.PublishedPort}`,
            type: 'web',
            status: service.State === 'running' ? 'healthy' : 'unhealthy'
          });
        }
      }
    }

    return endpoints;
  }

  // Méthodes utilitaires

  private getExposedPorts(request: DeploymentRequest): Record<string, {}> {
    const exposedPorts: Record<string, {}> = {};
    
    if (request.configuration.networking?.ports) {
      for (const port of request.configuration.networking.ports) {
        exposedPorts[`${port.targetPort}/tcp`] = {};
      }
    } else {
      exposedPorts['3000/tcp'] = {};
    }

    return exposedPorts;
  }

  private getPortBindings(request: DeploymentRequest): Record<string, any[]> {
    const portBindings: Record<string, any[]> = {};
    
    if (request.configuration.networking?.ports) {
      for (const port of request.configuration.networking.ports) {
        portBindings[`${port.targetPort}/tcp`] = [{ HostPort: port.port.toString() }];
      }
    } else {
      portBindings['3000/tcp'] = [{ HostPort: '3000' }];
    }

    return portBindings;
  }

  private parseMemory(memoryLimit?: string): number | undefined {
    if (!memoryLimit) return undefined;
    
    const match = memoryLimit.match(/^(\d+)([KMGT]?i?)$/);
    if (!match) return undefined;

    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();

    switch (unit) {
      case 'ki': return value * 1024;
      case 'mi': return value * 1024 * 1024;
      case 'gi': return value * 1024 * 1024 * 1024;
      case 'ti': return value * 1024 * 1024 * 1024 * 1024;
      default: return value;
    }
  }

  private parseCpu(cpuLimit?: string): number | undefined {
    if (!cpuLimit) return undefined;
    
    // Convertir les millicores en shares Docker (1 core = 1024 shares)
    if (cpuLimit.endsWith('m')) {
      const millicores = parseInt(cpuLimit.slice(0, -1));
      return Math.round((millicores / 1000) * 1024);
    }
    
    const cores = parseFloat(cpuLimit);
    return Math.round(cores * 1024);
  }

  private async executeCommand(command: string): Promise<string> {
    const { exec } = require('child_process');
    
    return new Promise((resolve, reject) => {
      exec(command, (error: any, stdout: string, stderr: string) => {
        if (error) {
          reject(new Error(`Command failed: ${command}\n${stderr}`));
        } else {
          resolve(stdout.trim());
        }
      });
    });
  }

  private getEmptyMetrics(): any {
    return {
      cpu: { current: 0, average: 0, peak: 0, unit: 'cores' },
      memory: { current: 0, average: 0, peak: 0, unit: 'MB' },
      network: { bytesIn: 0, bytesOut: 0, connectionsActive: 0, connectionsTotal: 0 },
      requests: { total: 0, rate: 0, latencyP50: 0, latencyP95: 0, latencyP99: 0 },
      errors: { total: 0, rate: 0, types: {} }
    };
  }
}
