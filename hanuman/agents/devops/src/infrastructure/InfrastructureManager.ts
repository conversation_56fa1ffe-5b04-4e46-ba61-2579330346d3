import { Logger } from 'winston';
import { 
  InfrastructureRequest,
  TerraformConfig,
  KubernetesConfig
} from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as yaml from 'yaml';
import { execSync } from 'child_process';

/**
 * Gestionnaire d'Infrastructure
 * 
 * Gère l'infrastructure as code avec Terraform et Kubernetes,
 * incluant la création, mise à jour et destruction des ressources.
 */
export class InfrastructureManager {
  private logger: Logger;
  private memory: WeaviateMemory;
  private terraformPath: string;
  private kubectlPath: string;
  private workingDirectory: string;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.terraformPath = process.env.TERRAFORM_PATH || 'terraform';
    this.kubectlPath = process.env.KUBECTL_PATH || 'kubectl';
    this.workingDirectory = process.env.INFRASTRUCTURE_WORKDIR || './infrastructure';

    // Créer le répertoire de travail s'il n'existe pas
    fs.ensureDirSync(this.workingDirectory);
  }

  /**
   * Crée une infrastructure
   */
  async createInfrastructure(request: InfrastructureRequest): Promise<any> {
    this.logger.info('Création de l\'infrastructure', { 
      id: request.id,
      provider: request.provider 
    });

    try {
      switch (request.provider) {
        case 'aws':
          return await this.createAWSInfrastructure(request);
        case 'gcp':
          return await this.createGCPInfrastructure(request);
        case 'azure':
          return await this.createAzureInfrastructure(request);
        case 'kubernetes':
          return await this.createKubernetesInfrastructure(request);
        default:
          throw new Error(`Provider ${request.provider} non supporté`);
      }
    } catch (error) {
      this.logger.error('Erreur lors de la création de l\'infrastructure', { 
        id: request.id,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Met à jour une infrastructure
   */
  async updateInfrastructure(request: InfrastructureRequest): Promise<any> {
    this.logger.info('Mise à jour de l\'infrastructure', { 
      id: request.id,
      provider: request.provider 
    });

    try {
      // Générer les nouveaux fichiers de configuration
      await this.generateInfrastructureFiles(request);

      // Planifier les changements
      const planResult = await this.planInfrastructure(request);

      // Appliquer les changements si approuvés
      if (planResult.approved) {
        return await this.applyInfrastructure(request);
      } else {
        return { status: 'pending-approval', plan: planResult };
      }
    } catch (error) {
      this.logger.error('Erreur lors de la mise à jour de l\'infrastructure', { 
        id: request.id,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Détruit une infrastructure
   */
  async destroyInfrastructure(request: InfrastructureRequest): Promise<any> {
    this.logger.info('Destruction de l\'infrastructure', { 
      id: request.id,
      provider: request.provider 
    });

    try {
      const projectPath = path.join(this.workingDirectory, request.id);

      if (request.provider === 'kubernetes') {
        return await this.destroyKubernetesInfrastructure(request);
      } else {
        // Utiliser Terraform pour détruire
        const result = await this.executeTerraform('destroy', projectPath, ['-auto-approve']);
        
        // Nettoyer les fichiers
        await fs.remove(projectPath);
        
        return {
          status: 'destroyed',
          resources: result.destroyed || [],
          timestamp: new Date()
        };
      }
    } catch (error) {
      this.logger.error('Erreur lors de la destruction de l\'infrastructure', { 
        id: request.id,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Obtient l'état de l'infrastructure
   */
  async getInfrastructureState(infrastructureId: string): Promise<any> {
    this.logger.info('Récupération de l\'état de l\'infrastructure', { infrastructureId });

    try {
      const projectPath = path.join(this.workingDirectory, infrastructureId);
      const stateFile = path.join(projectPath, 'terraform.tfstate');

      if (await fs.pathExists(stateFile)) {
        const state = await fs.readJson(stateFile);
        return {
          version: state.version,
          resources: state.resources || [],
          outputs: state.outputs || {},
          lastModified: state.serial
        };
      } else {
        return { status: 'not-found' };
      }
    } catch (error) {
      this.logger.error('Erreur lors de la récupération de l\'état', { 
        infrastructureId,
        error: error.message 
      });
      throw error;
    }
  }

  // Méthodes privées pour AWS

  private async createAWSInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    await fs.ensureDir(projectPath);

    // Générer les fichiers Terraform pour AWS
    await this.generateAWSTerraformFiles(request, projectPath);

    // Initialiser et appliquer Terraform
    await this.executeTerraform('init', projectPath);
    const planResult = await this.executeTerraform('plan', projectPath);
    const applyResult = await this.executeTerraform('apply', projectPath, ['-auto-approve']);

    return {
      status: 'created',
      provider: 'aws',
      resources: applyResult.resources || [],
      outputs: applyResult.outputs || {},
      timestamp: new Date()
    };
  }

  private async generateAWSTerraformFiles(request: InfrastructureRequest, projectPath: string): Promise<void> {
    // Provider configuration
    const providerConfig = `
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "${request.configuration.region}"
}
`;

    // Variables
    const variablesConfig = `
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "${request.configuration.environment}"
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "${request.id}"
}
`;

    // Resources
    let resourcesConfig = '';
    for (const resource of request.resources) {
      resourcesConfig += this.generateAWSResource(resource);
    }

    // Outputs
    const outputsConfig = `
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}
`;

    // Écrire les fichiers
    await fs.writeFile(path.join(projectPath, 'main.tf'), providerConfig);
    await fs.writeFile(path.join(projectPath, 'variables.tf'), variablesConfig);
    await fs.writeFile(path.join(projectPath, 'resources.tf'), resourcesConfig);
    await fs.writeFile(path.join(projectPath, 'outputs.tf'), outputsConfig);
  }

  private generateAWSResource(resource: any): string {
    switch (resource.type) {
      case 'vpc':
        return `
resource "aws_vpc" "main" {
  cidr_block           = "${resource.configuration.cidr_block || '10.0.0.0/16'}"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "\${var.project_name}-vpc"
    Environment = var.environment
  }
}
`;

      case 'subnet':
        return `
resource "aws_subnet" "public" {
  count             = ${resource.configuration.count || 2}
  vpc_id            = aws_vpc.main.id
  cidr_block        = cidrsubnet(aws_vpc.main.cidr_block, 8, count.index)
  availability_zone = data.aws_availability_zones.available.names[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name        = "\${var.project_name}-public-\${count.index + 1}"
    Environment = var.environment
  }
}

data "aws_availability_zones" "available" {
  state = "available"
}
`;

      case 'eks_cluster':
        return `
resource "aws_eks_cluster" "main" {
  name     = "\${var.project_name}-cluster"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "${resource.configuration.version || '1.27'}"

  vpc_config {
    subnet_ids = aws_subnet.public[*].id
  }

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
  ]

  tags = {
    Name        = "\${var.project_name}-cluster"
    Environment = var.environment
  }
}
`;

      default:
        return `# Resource type ${resource.type} not implemented\n`;
    }
  }

  // Méthodes privées pour GCP

  private async createGCPInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    await fs.ensureDir(projectPath);

    // Générer les fichiers Terraform pour GCP
    await this.generateGCPTerraformFiles(request, projectPath);

    // Initialiser et appliquer Terraform
    await this.executeTerraform('init', projectPath);
    const applyResult = await this.executeTerraform('apply', projectPath, ['-auto-approve']);

    return {
      status: 'created',
      provider: 'gcp',
      resources: applyResult.resources || [],
      outputs: applyResult.outputs || {},
      timestamp: new Date()
    };
  }

  private async generateGCPTerraformFiles(request: InfrastructureRequest, projectPath: string): Promise<void> {
    const providerConfig = `
terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = "${request.configuration.region}"
}
`;

    await fs.writeFile(path.join(projectPath, 'main.tf'), providerConfig);
    // Ajouter d'autres fichiers GCP...
  }

  // Méthodes privées pour Azure

  private async createAzureInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    await fs.ensureDir(projectPath);

    // Générer les fichiers Terraform pour Azure
    await this.generateAzureTerraformFiles(request, projectPath);

    // Initialiser et appliquer Terraform
    await this.executeTerraform('init', projectPath);
    const applyResult = await this.executeTerraform('apply', projectPath, ['-auto-approve']);

    return {
      status: 'created',
      provider: 'azure',
      resources: applyResult.resources || [],
      outputs: applyResult.outputs || {},
      timestamp: new Date()
    };
  }

  private async generateAzureTerraformFiles(request: InfrastructureRequest, projectPath: string): Promise<void> {
    const providerConfig = `
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}
`;

    await fs.writeFile(path.join(projectPath, 'main.tf'), providerConfig);
    // Ajouter d'autres fichiers Azure...
  }

  // Méthodes privées pour Kubernetes

  private async createKubernetesInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    await fs.ensureDir(projectPath);

    // Générer les manifests Kubernetes
    const manifests = await this.generateKubernetesManifests(request);

    // Appliquer les manifests
    for (const manifest of manifests) {
      const manifestPath = path.join(projectPath, `${manifest.metadata.name}.yaml`);
      await fs.writeFile(manifestPath, yaml.stringify(manifest));
      
      await this.executeKubectl('apply', ['-f', manifestPath]);
    }

    return {
      status: 'created',
      provider: 'kubernetes',
      resources: manifests.map(m => ({
        type: m.kind.toLowerCase(),
        name: m.metadata.name,
        namespace: m.metadata.namespace || 'default'
      })),
      timestamp: new Date()
    };
  }

  private async generateKubernetesManifests(request: InfrastructureRequest): Promise<any[]> {
    const manifests = [];

    for (const resource of request.resources) {
      switch (resource.type) {
        case 'namespace':
          manifests.push({
            apiVersion: 'v1',
            kind: 'Namespace',
            metadata: {
              name: resource.name,
              labels: resource.configuration.labels || {}
            }
          });
          break;

        case 'deployment':
          manifests.push({
            apiVersion: 'apps/v1',
            kind: 'Deployment',
            metadata: {
              name: resource.name,
              namespace: resource.configuration.namespace || 'default'
            },
            spec: {
              replicas: resource.configuration.replicas || 1,
              selector: {
                matchLabels: { app: resource.name }
              },
              template: {
                metadata: {
                  labels: { app: resource.name }
                },
                spec: {
                  containers: resource.configuration.containers || []
                }
              }
            }
          });
          break;

        case 'service':
          manifests.push({
            apiVersion: 'v1',
            kind: 'Service',
            metadata: {
              name: resource.name,
              namespace: resource.configuration.namespace || 'default'
            },
            spec: {
              selector: { app: resource.configuration.app || resource.name },
              ports: resource.configuration.ports || [],
              type: resource.configuration.type || 'ClusterIP'
            }
          });
          break;
      }
    }

    return manifests;
  }

  private async destroyKubernetesInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    
    if (await fs.pathExists(projectPath)) {
      // Supprimer tous les manifests
      const files = await fs.readdir(projectPath);
      const yamlFiles = files.filter(f => f.endsWith('.yaml'));

      for (const file of yamlFiles) {
        const filePath = path.join(projectPath, file);
        await this.executeKubectl('delete', ['-f', filePath]);
      }

      // Nettoyer le répertoire
      await fs.remove(projectPath);
    }

    return {
      status: 'destroyed',
      resources: [],
      timestamp: new Date()
    };
  }

  // Méthodes utilitaires

  private async generateInfrastructureFiles(request: InfrastructureRequest): Promise<void> {
    const projectPath = path.join(this.workingDirectory, request.id);
    await fs.ensureDir(projectPath);

    switch (request.provider) {
      case 'aws':
        await this.generateAWSTerraformFiles(request, projectPath);
        break;
      case 'gcp':
        await this.generateGCPTerraformFiles(request, projectPath);
        break;
      case 'azure':
        await this.generateAzureTerraformFiles(request, projectPath);
        break;
    }
  }

  private async planInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    const planResult = await this.executeTerraform('plan', projectPath, ['-out=tfplan']);
    
    return {
      approved: true, // Simplification - en réalité, cela nécessiterait une approbation
      changes: planResult.changes || [],
      plan: planResult
    };
  }

  private async applyInfrastructure(request: InfrastructureRequest): Promise<any> {
    const projectPath = path.join(this.workingDirectory, request.id);
    const applyResult = await this.executeTerraform('apply', projectPath, ['tfplan']);
    
    return {
      status: 'updated',
      resources: applyResult.resources || [],
      outputs: applyResult.outputs || {},
      timestamp: new Date()
    };
  }

  private async executeTerraform(command: string, workingDir: string, args: string[] = []): Promise<any> {
    const fullCommand = `${this.terraformPath} ${command} ${args.join(' ')}`;
    
    this.logger.info('Exécution de Terraform', { command: fullCommand, workingDir });

    try {
      const output = execSync(fullCommand, { 
        cwd: workingDir,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      this.logger.info('Terraform exécuté avec succès', { command, output: output.substring(0, 500) });
      
      return {
        success: true,
        output,
        resources: this.parseTerraformOutput(output),
        outputs: this.parseTerraformOutputs(output)
      };
    } catch (error) {
      this.logger.error('Erreur lors de l\'exécution de Terraform', { 
        command,
        error: error.message,
        stderr: error.stderr?.toString()
      });
      throw error;
    }
  }

  private async executeKubectl(command: string, args: string[] = []): Promise<any> {
    const fullCommand = `${this.kubectlPath} ${command} ${args.join(' ')}`;
    
    this.logger.info('Exécution de kubectl', { command: fullCommand });

    try {
      const output = execSync(fullCommand, { 
        encoding: 'utf8',
        stdio: 'pipe'
      });

      this.logger.info('kubectl exécuté avec succès', { command, output: output.substring(0, 500) });
      
      return {
        success: true,
        output
      };
    } catch (error) {
      this.logger.error('Erreur lors de l\'exécution de kubectl', { 
        command,
        error: error.message
      });
      throw error;
    }
  }

  private parseTerraformOutput(output: string): any[] {
    // Parser la sortie Terraform pour extraire les ressources créées
    const resources = [];
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes('created') || line.includes('modified')) {
        const match = line.match(/(\w+\.\w+)/);
        if (match) {
          resources.push({
            type: match[1].split('.')[0],
            name: match[1].split('.')[1],
            action: line.includes('created') ? 'created' : 'modified'
          });
        }
      }
    }
    
    return resources;
  }

  private parseTerraformOutputs(output: string): any {
    // Parser les outputs Terraform
    const outputs = {};
    const lines = output.split('\n');
    
    let inOutputsSection = false;
    for (const line of lines) {
      if (line.includes('Outputs:')) {
        inOutputsSection = true;
        continue;
      }
      
      if (inOutputsSection && line.includes(' = ')) {
        const [key, value] = line.split(' = ');
        outputs[key.trim()] = value.trim().replace(/"/g, '');
      }
    }
    
    return outputs;
  }
}
