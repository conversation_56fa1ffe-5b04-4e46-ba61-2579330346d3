version: '3.8'

services:
  # Agent Frontend Principal
  agent-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: agent-frontend
    ports:
      - "3006:3006"
      - "9090:9090" # Métriques
    environment:
      - NODE_ENV=production
      - PORT=3006
      - AGENT_ID=agent-frontend-001
      - WEAVIATE_URL=http://weaviate:8080
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./generated:/app/generated
      - ./templates:/app/templates
    depends_on:
      - weaviate
      - kafka
      - redis
    networks:
      - agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Base de données vectorielle Weaviate
  weaviate:
    image: semitechnologies/weaviate:1.21.2
    container_name: weaviate-frontend
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - agent-network
    restart: unless-stopped

  # Message Broker Kafka
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-frontend
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_NODE_ID: 1
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka:29093'
      KAFKA_LISTENERS: 'PLAINTEXT://kafka:29092,CONTROLLER://kafka:29093,PLAINTEXT_HOST://0.0.0.0:9092'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LOG_DIRS: '/tmp/kraft-combined-logs'
      CLUSTER_ID: 'MkU3OEVBNTcwNTJENDM2Qk'
    volumes:
      - kafka_data:/tmp/kraft-combined-logs
    networks:
      - agent-network
    restart: unless-stopped

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: redis-frontend
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - agent-network
    restart: unless-stopped

  # Interface de gestion Kafka
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui-frontend
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_JMXPORT: 9101
    depends_on:
      - kafka
    networks:
      - agent-network
    restart: unless-stopped

  # Monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-frontend
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - agent-network
    restart: unless-stopped

  # Visualisation avec Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-frontend
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - agent-network
    restart: unless-stopped

  # Collecteur de logs
  loki:
    image: grafana/loki:latest
    container_name: loki-frontend
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - loki_data:/loki
    networks:
      - agent-network
    restart: unless-stopped

  # Agent de logs
  promtail:
    image: grafana/promtail:latest
    container_name: promtail-frontend
    volumes:
      - ./logs:/var/log/agent
      - ./monitoring/promtail.yml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - agent-network
    restart: unless-stopped

  # Service de test et développement
  agent-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: agent-frontend-dev
    ports:
      - "3007:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - AGENT_ID=agent-frontend-dev-001
      - WEAVIATE_URL=http://weaviate:8080
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
      - ENABLE_HOT_RELOAD=true
      - MOCK_EXTERNAL_SERVICES=true
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    depends_on:
      - weaviate
      - kafka
      - redis
    networks:
      - agent-network
    restart: unless-stopped
    profiles:
      - dev

volumes:
  weaviate_data:
    driver: local
  kafka_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

networks:
  agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
