import { Logger } from 'winston';
import { ValidationResult, GeneratedCode, ComprehensiveUXDesign } from '../types';
/**
 * Validateur de Code Frontend
 *
 * Valide le code généré pour s'assurer qu'il respecte
 * les standards de qualité, d'accessibilité et de performance.
 */
export declare class CodeValidator {
    private logger;
    constructor(logger: Logger);
    /**
     * Valide le code généré
     */
    validateCode(generatedCode: GeneratedCode, originalDesign: ComprehensiveUXDesign): Promise<ValidationResult>;
    /**
     * Analyse le code existant
     */
    analyzeExistingCode(code: string, framework: string): Promise<any>;
    /**
     * Vérifie la conformité au design
     */
    checkDesignCompliance(codeAnalysis: any, design: ComprehensiveUXDesign): Promise<any>;
    /**
     * Validation syntaxique
     */
    private validateSyntax;
    /**
     * Validation d'accessibilité
     */
    validateAccessibility(codeOrString: GeneratedCode | string): Promise<any>;
    /**
     * Validation de performance
     */
    validatePerformance(codeOrString: GeneratedCode | string): Promise<any>;
    /**
     * Validation de sécurité
     */
    validateSecurity(codeOrString: GeneratedCode | string): Promise<any>;
    /**
     * Validation de la qualité du code
     */
    private validateCodeQuality;
    private validateJSXSyntax;
    private validateCSSSyntax;
    private checkAltAttributes;
    private checkFormLabels;
    private checkHeadingStructure;
    private checkAriaAttributes;
    private checkLazyLoading;
    private checkBundleOptimization;
    private findUnusedImports;
    private checkDangerousHTML;
    private checkExposedSecrets;
    private calculateComplexity;
    private checkCodeDuplication;
    private calculateOverallScore;
    private generateSuggestions;
    private calculateTestCoverage;
    private calculateMaintainability;
    private analyzeTestCoverage;
    private analyzeDependencies;
    private analyzePerformance;
    private analyzeSecurity;
    private analyzeAccessibility;
    private calculateQualityScore;
    private checkColorCompliance;
    private checkTypographyCompliance;
    private checkSpacingCompliance;
}
//# sourceMappingURL=CodeValidator.d.ts.map