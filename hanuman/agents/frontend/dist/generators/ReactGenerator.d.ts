import { Logger } from 'winston';
import { ComprehensiveUXDesign, CodeGenerationRequest, GeneratedCode, Template } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Générateur React Spécialisé
 *
 * Génère du code React optimisé à partir des designs UI/UX,
 * incluant composants, pages, hooks, services et configuration.
 */
export declare class ReactGenerator {
    private logger;
    private memory;
    private componentGenerator;
    private pageGenerator;
    private hookGenerator;
    private serviceGenerator;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Génère une application React complète à partir d'un design
     */
    generateFromDesign(design: ComprehensiveUXDesign, request: CodeGenerationRequest, templates: Template[]): Promise<GeneratedCode>;
    /**
     * Génère la structure de base du projet
     */
    private generateProjectStructure;
    /**
     * Génère les composants UI à partir du design system
     */
    private generateUIComponents;
    /**
     * G<PERSON><PERSON> les pages à partir des wireframes
     */
    private generatePages;
    /**
     * G<PERSON>ère les hooks personnalisés
     */
    private generateCustomHooks;
    /**
     * Génère les services
     */
    private generateServices;
    /**
     * <PERSON><PERSON><PERSON> le système de routing
     */
    private generateRouting;
    /**
     * Génère la gestion d'état
     */
    private generateStateManagement;
    /**
     * Génère les styles
     */
    private generateStyles;
    /**
     * Génère la configuration du projet
     */
    private generateConfiguration;
    private generatePackageJson;
    private generateAppComponent;
    private calculateMetrics;
    private calculateQualityMetrics;
    private generateReadme;
    private generateIndexHtml;
    private generateMainEntry;
    private generateTsConfig;
    private generateProjectStructureInfo;
    private generateDependencies;
    private generateScripts;
    private generateProjectConfiguration;
    private generateAppRouter;
    private generateRoutesConfig;
    private generateZustandStore;
    private generateReduxStore;
    private generateContextStore;
    private generateTailwindStyles;
    private generateScssStyles;
    private generateStyledComponents;
    private generateCssStyles;
    private generateViteConfig;
    private generateEslintConfig;
    private generatePrettierConfig;
}
//# sourceMappingURL=ReactGenerator.d.ts.map