import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { ComprehensiveUXDesign, CodeGenerationRequest, GeneratedCode, ValidationResult, AgentConfig } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Agent Frontend - Générateur de Code Automatique
 *
 * Cet agent reçoit les designs de l'Agent UI/UX et génère automatiquement
 * du code React/Vue optimisé, accessible et performant.
 */
export declare class FrontendAgent extends EventEmitter {
    private logger;
    private config;
    private memory;
    private communication;
    private reactGenerator;
    private vueGenerator;
    private codeValidator;
    private templateManager;
    private optimizationEngine;
    constructor(config: AgentConfig, logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Point d'entrée principal pour générer du code à partir d'un design
     */
    generateCodeFromDesign(design: ComprehensiveUXDesign, request: CodeGenerationRequest): Promise<GeneratedCode>;
    /**
     * Valide une implémentation existante par rapport au design original
     */
    validateImplementation(code: string, design: ComprehensiveUXDesign, framework: string): Promise<ValidationResult>;
    /**
     * Génère des améliorations pour un code existant
     */
    generateImprovements(code: string, design: ComprehensiveUXDesign, framework: string): Promise<GeneratedCode>;
    /**
     * Déploie automatiquement le code généré
     */
    deployCode(generatedCode: GeneratedCode, deploymentConfig: any): Promise<any>;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Gestionnaire de réception de design
     */
    private handleDesignReceived;
    /**
     * Gestionnaire de demande de validation
     */
    private handleValidationRequest;
    /**
     * Gestionnaire de demande d'amélioration
     */
    private handleImprovementRequest;
    private validateGenerationRequest;
    private applyAutomaticFixes;
    private generateDocumentation;
    private generateTests;
    private calculateOverallScore;
    private identifyImprovements;
    private applyImprovements;
    private applyFix;
    private generateReadme;
    private generateApiDocs;
    private generateComponentDocs;
    private generateDeploymentGuide;
    private generateDevelopmentGuide;
    private generateChangelog;
    private generateUnitTests;
    private generateIntegrationTests;
    private generateAccessibilityTests;
    private generateImprovementSuggestions;
    private prepareDeploymentPackage;
    private deployToplatform;
    private setupMonitoring;
}
//# sourceMappingURL=FrontendAgent.d.ts.map