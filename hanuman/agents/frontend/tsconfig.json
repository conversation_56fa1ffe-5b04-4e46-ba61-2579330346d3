{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types": ["types/index"], "@/core": ["core/*"], "@/generators": ["generators/*"], "@/validators": ["validators/*"], "@/optimization": ["optimization/*"], "@/memory": ["memory/*"], "@/communication": ["communication/*"], "@/templates": ["templates/*"], "@/utils": ["utils/*"]}}, "include": ["src/**/*", "src/**/*.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}