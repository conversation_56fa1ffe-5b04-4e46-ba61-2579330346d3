import { Logger } from 'winston';
import { AgentConfig, AutoDeployment } from '../types';
/**
 * Moteur Auto-Deployment - Déploiement automatisé avec rollback
 */
export declare class AutoDeploymentEngine {
    private logger;
    private config;
    private docker;
    private activeDeployments;
    private scheduledTasks;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Crée un déploiement automatique
     */
    createDeployment(config: any): Promise<AutoDeployment>;
    /**
     * Active un déploiement automatique
     */
    activateDeployment(deploymentId: string): Promise<void>;
    /**
     * Exécute un déploiement
     */
    executeDeployment(deploymentId: string, context?: any): Promise<any>;
    /**
     * Exécute un rollback
     */
    executeRollback(deployment: AutoDeployment, context: any): Promise<void>;
    /**
     * Arrête l'auto-déploiement
     */
    stopAutoDeployment(): Promise<void>;
    private configureTrigger;
    private configureTarget;
    private configurePipeline;
    private configureRollback;
    private configureMonitoring;
    private setupTriggers;
    private setupScheduleTrigger;
    private setupWebhookTrigger;
    private setupEventTrigger;
    private checkConditions;
    private executePipeline;
    private executeStep;
    private monitorDeployment;
    private immediateRollback;
    private gradualRollback;
    private requestManualApproval;
    private performHealthCheck;
    private checkTestResults;
    private checkApproval;
    private collectMetrics;
    private sendNotifications;
    private shouldSendNotification;
    private sendNotification;
    private generateId;
}
//# sourceMappingURL=AutoDeploymentEngine.d.ts.map