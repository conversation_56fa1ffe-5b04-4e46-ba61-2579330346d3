import { Logger } from 'winston';
import { AgentConfig, UXTrend } from '../types';
/**
 * Moteur UX Trend - Analyse des tendances UX et design
 */
export declare class UXTrendEngine {
    private logger;
    private config;
    private isRunning;
    private analysisTask?;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Collecte les données UX
     */
    collectUXData(): Promise<any[]>;
    /**
     * Analyse les tendances UX
     */
    analyzeTrends(uxData: any[]): Promise<UXTrend[]>;
    /**
     * Évalue l'impact des tendances
     */
    evaluateImpact(trends: UXTrend[]): Promise<UXTrend[]>;
    /**
     * Démarre l'analyse continue
     */
    startContinuousAnalysis(): Promise<void>;
    /**
     * Arrête l'analyse continue
     */
    stopContinuousAnalysis(): Promise<void>;
    /**
     * Traite les données de recherche web
     */
    processWebResearchData(data: any): Promise<void>;
    private collectDesignTrends;
    private collectUserStudies;
    private collectPlatformTrends;
    private groupByCategory;
    private analyzeCategoryTrends;
    private createUXTrend;
    private identifyEmergingTrends;
    private evaluateTrendImpact;
    private generateRecommendations;
    private calculateTrendScore;
    private performScheduledAnalysis;
    private extractDesignMentions;
    private analyzeUXTrends;
    private updateAdoptionScores;
    private determineTrendType;
    private determineTrendStatus;
    private determineImpact;
    private calculateAdoptionRate;
    private calculateMaturityLevel;
    private assessDifficulty;
    private estimateImplementationTime;
    private estimateCost;
    private generateId;
}
//# sourceMappingURL=UXTrendEngine.d.ts.map