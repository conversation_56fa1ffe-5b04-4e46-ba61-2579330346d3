import { EventEmitter } from 'events';
import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { Logger } from '../utils/logger';
/**
 * Dashboard de Monitoring Synaptique
 *
 * Fournit une interface de monitoring en temps réel pour la neuroplasticité,
 * incluant des métriques, visualisations et alertes.
 */
export declare class NeuroplasticityDashboard extends EventEmitter {
    private engine;
    private logger;
    private isMonitoring;
    private monitoringInterval;
    private metricsHistory;
    private alerts;
    private thresholds;
    constructor(engine: NeuroplasticityEngine, logger: Logger);
    /**
     * Démarre le monitoring
     */
    startMonitoring(intervalMs?: number): Promise<void>;
    /**
     * Arrête le monitoring
     */
    stopMonitoring(): void;
    /**
     * Collecte les métriques actuelles
     */
    private collectMetrics;
    /**
     * Vérifie les seuils et génère des alertes
     */
    private checkThresholds;
    /**
     * Crée une alerte
     */
    private createAlert;
    /**
     * Configure les listeners sur le moteur
     */
    private setupEngineListeners;
    /**
     * Calcule l'efficacité moyenne des patterns
     */
    private calculateAverageEfficiency;
    /**
     * Calcule le potentiel d'optimisation total
     */
    private calculateTotalOptimizationPotential;
    /**
     * Calcule la santé globale du réseau
     */
    private calculateNetworkHealth;
    /**
     * Détermine le statut de santé
     */
    private getHealthStatus;
    /**
     * Obtient les métriques actuelles
     */
    getCurrentMetrics(): DashboardMetrics | null;
    /**
     * Obtient l'historique des métriques
     */
    getMetricsHistory(limit?: number): DashboardMetrics[];
    /**
     * Obtient les alertes actives
     */
    getActiveAlerts(): PlasticityAlert[];
    /**
     * Génère un rapport de santé
     */
    generateHealthReport(): HealthReport;
    /**
     * Génère des recommandations
     */
    private generateRecommendations;
    /**
     * Génère un résumé
     */
    private generateSummary;
}
interface DashboardMetrics {
    timestamp: Date;
    plasticity: any;
    patterns: {
        totalPatterns: number;
        efficientPatterns: number;
        averageEfficiency: number;
        topPatterns: any[];
    };
    optimization: {
        suboptimalPaths: number;
        optimizationPotential: number;
        topBottlenecks: any[];
    };
    health: NetworkHealth;
}
interface NetworkHealth {
    score: number;
    status: string;
    components: {
        efficiency: number;
        latency: number;
        connections: number;
        patterns: number;
    };
}
interface PlasticityAlert {
    type: string;
    severity: 'info' | 'warning' | 'critical';
    message: string;
    value: number;
    threshold: number;
    timestamp: Date;
}
interface HealthReport {
    timestamp: Date;
    status: string;
    summary: string;
    metrics: DashboardMetrics | null;
    alerts: PlasticityAlert[];
    recommendations: string[];
}
export {};
//# sourceMappingURL=NeuroplasticityDashboard.d.ts.map