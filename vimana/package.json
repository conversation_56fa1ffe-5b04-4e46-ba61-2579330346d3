{"name": "vimana-divine-framework", "version": "0.1.0", "description": "VIMANA - Divine Agentic Coding Framework for Sacred Software Creation", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "vimana:start": "./vimana-start.sh", "vimana:bless": "echo \"🕉️ AUM VIMANA PROJECT NAMAHA 🕉️\"", "vimana:validate": "echo \"Divine validation coming soon...\"", "vimana:create": "echo \"🕉️ Invoking Brahma Creator... Feature creation coming soon\"", "vimana:test": "echo \"🕉️ Invoking Vishnu Preserver... Testing framework coming soon\"", "vimana:refactor": "echo \"🕉️ Invoking Shiva Transformer... Refactoring tools coming soon\"", "vimana:install": "npm install && echo \"🕉️ Divine dependencies installed\"", "vimana:setup": "npm run vimana:install && npm run vimana:bless"}, "dependencies": {"@nestjs/common": "^8.0.0", "@nestjs/config": "^2.0.0", "@nestjs/core": "^8.0.0", "@nestjs/jwt": "^8.0.0", "@nestjs/passport": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/swagger": "^5.2.0", "@prisma/client": "^3.12.0", "axios": "^0.21.1", "bcrypt": "^5.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "lodash": "^4.17.21", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "swagger-ui-express": "^4.3.0"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/jest": "27.4.1", "@types/lodash": "^4.14.181", "@types/node": "^16.0.0", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "prisma": "^3.12.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "homepage": "https://vimana-divine.dev"}