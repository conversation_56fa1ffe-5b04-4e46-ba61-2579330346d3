# 🚁 VIMANA - Guide de Démarrage Rapide

## 🕉️ Installation Divine

### Étape 1 : Installation des Dépendances
```bash
# Entrer dans l'espace sacré
cd vimana

# Installer les dépendances divines
npm run vimana:setup
```

### Étape 2 : Vérification de l'Installation
```bash
# Vérifier que tout est prêt
npm run vimana:start
```

## 🌟 Utilisation de Base

### Commandes Divines Disponibles

```bash
# Démarrage du framework
npm run vimana:start

# Bénédiction du projet
npm run vimana:bless

# Invocation des agents divins
npm run vimana:create    # Brahma Creator
npm run vimana:test      # Vishnu Preserver  
npm run vimana:refactor  # Shiva Transformer

# Validation divine
npm run vimana:validate
```

### Workflow Automatisé

1. **Initialisation :**
   ```bash
   npm run vimana:start
   ```

2. **Lancement du workflow :**
   - Ouvrir `01_AI-RUN/01_AutoPilot.md`
   - Suivre les instructions pour le workflow automatisé

3. **Phases du développement :**
   - Phase 1 : Idée (`01_Idea.md`)
   - Phase 2 : Recherche marché (`02_Market_Research.md`)
   - Phase 3 : Concept (`03_Core_Concept.md`)
   - Phase 4 : PRD (`04_PRD_Generation.md`)
   - Phase 5 : Spécifications (`05_Specs_Docs.md`)
   - Phase 6 : Gestion tâches (`06_Task_Manager.md`)
   - Phase 7 : Implémentation (`07_Start_Building.md`)
   - Phase 8 : Tests (`08_Testing.md`)
   - Phase 9 : Déploiement (`09_Deployment.md`)

## 📊 État du Projet

Le framework VIMANA est actuellement configuré pour le projet **Retreat And Be** :
- Nom : "Retreat And Be"
- Type : "web_platform"
- Phase actuelle : "development"
- Dernière étape : "task_management_completed"

## 🔧 Configuration

La configuration divine se trouve dans `vimana.config.js` avec :
- Principes cosmiques (nombre d'or φ, fréquences sacrées)
- Balance Tri-Guna (Sattva, Rajas, Tamas)
- Standards de développement divins
- Configuration des agents IA

## 📚 Documentation

- `README.md` - Documentation principale
- `workflow.md` - Diagramme de workflow
- `01_AI-RUN/00_Getting_Started.md` - Guide détaillé
- `02_AI-DOCS/` - Documentation technique
- `03_SPECS/` - Spécifications

## 🕉️ Mantras de Développement

**Création :** `AUM BRAHMAYE NAMAHA`  
**Préservation :** `AUM VISHNAVE NAMAHA`  
**Transformation :** `AUM SHIVAYA NAMAHA`

---

*"Que votre code soit béni par la sagesse cosmique !"* 🚁✨
